#!/usr/bin/env python3
"""
Enhanced main entry point for the AI agent.

This module provides the command-line interface and main execution logic
for the enhanced AI agent system with advanced capabilities.
"""

import sys
import argparse
import logging
import asyncio
from pathlib import Path

from cli import main as cli_main
from server.web import main as web_main
from config import get_config
from utils.logging_setup import setup_logging

# Enhanced components
from enhanced_agent import create_enhanced_agent


def enhanced_cli_main(
    workspace: Path = None,
    model: str = None,
    tools: list = None,
    auto_confirm: bool = False,
    enhanced: bool = True
):
    """Enhanced CLI main function."""
    if enhanced:
        # Use enhanced agent
        agent = create_enhanced_agent(workspace)
        
        print("🚀 Enhanced AI Agent - Interactive Mode")
        print("=" * 50)
        print(f"Workspace: {agent.workspace_path}")
        print(f"Project Type: {agent.context.project_type or 'Unknown'}")
        print(f"Available Tools: {len(agent.list_available_tools())}")
        print("\nType 'help' for commands, 'exit' to quit")
        print("-" * 50)
        
        try:
            while True:
                try:
                    user_input = input("\n💬 You: ").strip()
                    
                    if not user_input:
                        continue
                    
                    if user_input.lower() in ['exit', 'quit', 'q']:
                        break
                    
                    if user_input.lower() == 'help':
                        print_enhanced_help()
                        continue
                    
                    if user_input.lower() == 'status':
                        print(agent.get_session_summary())
                        continue
                    
                    if user_input.lower() == 'tools':
                        tools_list = agent.list_available_tools()
                        print(f"\n📋 Available Tools ({len(tools_list)}):")
                        for tool in tools_list[:20]:  # Show first 20
                            status = "✅" if tool["available"] else "❌"
                            print(f"  {status} {tool['name']}: {tool['description'][:60]}...")
                        if len(tools_list) > 20:
                            print(f"  ... and {len(tools_list) - 20} more tools")
                        continue
                    
                    if user_input.lower() == 'workflows':
                        templates = agent.get_workflow_templates()
                        print(f"\n🔄 Available Workflows ({len(templates)}):")
                        for template in templates:
                            print(f"  • {template['name']}: {template['description']}")
                        continue
                    
                    # Process user input with enhanced agent
                    print("\n🤖 Agent:")
                    for message in agent.process_user_input(user_input, auto_execute=auto_confirm):
                        if message.content:
                            print(f"  {message.content}")
                
                except KeyboardInterrupt:
                    print("\n\n⚠️ Interrupted by user")
                    continue
                except Exception as e:
                    print(f"\n❌ Error: {e}")
                    continue
        
        finally:
            print("\n💾 Saving session...")
            agent.save_session()
            print("👋 Goodbye!")
    
    else:
        # Use original CLI
        return cli_main(workspace, model, tools, auto_confirm)


def print_enhanced_help():
    """Print enhanced help information."""
    help_text = """
🆘 Enhanced AI Agent Help

📋 Commands:
  help      - Show this help message
  status    - Show current session status
  tools     - List available tools
  workflows - List available workflows
  exit/quit - Exit the agent

💡 Example Queries:
  "analyze the codebase structure"
  "find all Python functions in this project"
  "create a new module for user authentication"
  "run tests and show coverage"
  "search for TODO comments"
  "refactor the database module"
  "generate documentation for the API"

🔧 Features:
  • Context-aware tool selection
  • Intelligent workflow automation
  • Large codebase optimization
  • Performance monitoring
  • Error recovery and alternatives

💬 Tips:
  • Be specific about what you want to accomplish
  • The agent learns from your usage patterns
  • Use natural language - no special syntax required
  • Check tool suggestions before auto-execution
"""
    print(help_text)


async def demo_main():
    """Run enhanced agent demo."""
    try:
        from examples.enhanced_agent_demo import main as demo_main_func
        await demo_main_func()
        return 0
    except ImportError:
        print("❌ Demo module not available")
        return 1


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="AI Agent - Intelligent coding assistant with enhanced capabilities",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Add subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # CLI command
    cli_parser = subparsers.add_parser('cli', help='Run in CLI mode')
    cli_parser.add_argument(
        '--workspace', '-w',
        type=Path,
        help='Workspace directory'
    )
    cli_parser.add_argument(
        '--model', '-m',
        help='LLM model to use'
    )
    cli_parser.add_argument(
        '--tools', '-t',
        nargs='+',
        help='Tools to enable'
    )
    cli_parser.add_argument(
        '--auto-confirm', '-y',
        action='store_true',
        help='Auto-confirm tool executions'
    )
    cli_parser.add_argument(
        '--enhanced', '-e',
        action='store_true',
        default=True,
        help='Use enhanced agent (default: True)'
    )
    cli_parser.add_argument(
        '--legacy',
        action='store_true',
        help='Use legacy agent instead of enhanced'
    )
    cli_parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    # Web server command
    web_parser = subparsers.add_parser('web', help='Run web server')
    web_parser.add_argument(
        '--host',
        default='127.0.0.1',
        help='Host to bind to'
    )
    web_parser.add_argument(
        '--port', '-p',
        type=int,
        default=5000,
        help='Port to bind to'
    )
    web_parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode'
    )
    web_parser.add_argument(
        '--enhanced', '-e',
        action='store_true',
        default=True,
        help='Use enhanced agent features'
    )
    
    # Demo command
    demo_parser = subparsers.add_parser('demo', help='Run enhanced agent demo')
    demo_parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    
    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Quick codebase analysis')
    analyze_parser.add_argument(
        'path',
        nargs='?',
        default='.',
        help='Path to analyze (default: current directory)'
    )
    analyze_parser.add_argument(
        '--depth', '-d',
        type=int,
        default=3,
        help='Analysis depth (1-5, default: 3)'
    )
    analyze_parser.add_argument(
        '--format', '-f',
        choices=['summary', 'detailed', 'json'],
        default='detailed',
        help='Output format'
    )
    
    # Parse arguments
    args = parser.parse_args()
    
    # Setup logging
    config = get_config()
    log_level = logging.DEBUG if getattr(args, 'verbose', False) else logging.INFO
    setup_logging(level=log_level, log_dir=config.logs_dir)
    
    # Route to appropriate command
    if args.command == 'cli':
        enhanced = args.enhanced and not args.legacy
        return enhanced_cli_main(
            workspace=args.workspace,
            model=args.model,
            tools=args.tools,
            auto_confirm=args.auto_confirm,
            enhanced=enhanced
        )
    elif args.command == 'web':
        return web_main(
            host=args.host,
            port=args.port,
            debug=args.debug
        )
    elif args.command == 'demo':
        return asyncio.run(demo_main())
    elif args.command == 'analyze':
        # Quick analysis command
        agent = create_enhanced_agent(Path(args.path))
        print(f"🔍 Analyzing: {args.path}")
        for message in agent.analyze_codebase(depth=args.depth):
            if message.content:
                print(message.content)
        return 0
    else:
        # Default to enhanced CLI if no command specified
        return enhanced_cli_main(enhanced=True)


if __name__ == "__main__":
    sys.exit(main())
