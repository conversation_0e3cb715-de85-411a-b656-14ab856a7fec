"""
Context-aware tool selection system.

This module provides intelligent tool selection based on context, project type,
user intent, and historical usage patterns.
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from pathlib import Path
from dataclasses import dataclass, field
from collections import defaultdict, Counter
from datetime import datetime, timedelta

from tools.base import ToolSpec
from tools.enhanced_registry import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, ToolExecution
from config import get_config

logger = logging.getLogger(__name__)


@dataclass
class ToolRecommendation:
    """Represents a tool recommendation with confidence score."""
    tool_name: str
    confidence: float
    reasoning: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    alternatives: List[str] = field(default_factory=list)


@dataclass
class UserIntent:
    """Represents parsed user intent."""
    action: str  # create, modify, analyze, debug, test, etc.
    target: str  # file, function, class, project, etc.
    domain: str  # code, documentation, configuration, etc.
    urgency: str  # low, medium, high
    complexity: str  # simple, moderate, complex
    keywords: List[str] = field(default_factory=list)


class ContextAwareToolSelector:
    """Intelligent tool selection system."""
    
    def __init__(self):
        self._intent_patterns = self._load_intent_patterns()
        self._tool_mappings = self._load_tool_mappings()
        self._project_profiles = {}
        self._usage_history = []
        self._user_preferences = {}
        
    def _load_intent_patterns(self) -> Dict[str, List[str]]:
        """Load patterns for intent recognition."""
        return {
            "analyze": [
                r"analyze|analysis|examine|inspect|review|study|understand",
                r"what does|how does|explain|show me|tell me about",
                r"structure|architecture|dependencies|complexity"
            ],
            "create": [
                r"create|make|build|generate|add|new|write",
                r"implement|develop|code|program",
                r"file|function|class|module|component"
            ],
            "modify": [
                r"change|modify|update|edit|fix|refactor|improve",
                r"replace|remove|delete|rename|move",
                r"optimize|enhance|cleanup|format"
            ],
            "debug": [
                r"debug|fix|error|bug|issue|problem|broken",
                r"not working|fails|crash|exception",
                r"troubleshoot|diagnose|investigate"
            ],
            "test": [
                r"test|testing|verify|validate|check",
                r"unit test|integration test|coverage",
                r"assert|mock|fixture"
            ],
            "search": [
                r"find|search|locate|look for|where is",
                r"grep|pattern|match|contains",
                r"function|class|variable|import"
            ],
            "documentation": [
                r"document|docs|readme|comment|docstring",
                r"explain|describe|specification",
                r"api|guide|tutorial|example"
            ]
        }
    
    def _load_tool_mappings(self) -> Dict[str, Dict[str, List[str]]]:
        """Load mappings from intent/context to tools."""
        return {
            "analyze": {
                "code": ["codebase", "advanced_codebase", "python"],
                "project": ["codebase", "advanced_codebase", "file"],
                "dependencies": ["advanced_codebase", "shell"],
                "performance": ["advanced_codebase", "python"]
            },
            "create": {
                "file": ["file", "python"],
                "code": ["python", "file"],
                "project": ["shell", "file"],
                "documentation": ["file"]
            },
            "modify": {
                "code": ["file", "python", "codebase"],
                "file": ["file"],
                "configuration": ["file", "shell"]
            },
            "debug": {
                "code": ["python", "codebase", "shell"],
                "runtime": ["python", "shell"],
                "tests": ["python", "shell"]
            },
            "test": {
                "code": ["python", "shell"],
                "coverage": ["python", "shell"],
                "integration": ["shell", "python"]
            },
            "search": {
                "code": ["codebase", "advanced_codebase"],
                "files": ["codebase", "shell"],
                "content": ["codebase", "shell"]
            },
            "documentation": {
                "generate": ["file", "python"],
                "update": ["file"],
                "format": ["file"]
            }
        }
    
    def parse_user_intent(self, user_input: str, context: Optional[ToolContext] = None) -> UserIntent:
        """Parse user input to extract intent."""
        user_input_lower = user_input.lower()
        
        # Determine action
        action = "analyze"  # default
        for intent_action, patterns in self._intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, user_input_lower):
                    action = intent_action
                    break
            if action != "analyze":
                break
        
        # Determine target
        target = "code"  # default
        target_patterns = {
            "file": r"file|document|script",
            "function": r"function|method|def|func",
            "class": r"class|object|type",
            "project": r"project|codebase|repository|repo",
            "module": r"module|package|library",
            "configuration": r"config|settings|env|environment"
        }
        
        for target_type, pattern in target_patterns.items():
            if re.search(pattern, user_input_lower):
                target = target_type
                break
        
        # Determine domain
        domain = "code"  # default
        domain_patterns = {
            "documentation": r"doc|readme|comment|explain",
            "configuration": r"config|setting|env",
            "test": r"test|spec|coverage",
            "build": r"build|compile|deploy"
        }
        
        for domain_type, pattern in domain_patterns.items():
            if re.search(pattern, user_input_lower):
                domain = domain_type
                break
        
        # Determine urgency and complexity
        urgency = "medium"
        if re.search(r"urgent|asap|quickly|fast|immediately", user_input_lower):
            urgency = "high"
        elif re.search(r"when you can|no rush|eventually", user_input_lower):
            urgency = "low"
        
        complexity = "moderate"
        if re.search(r"simple|basic|quick|easy", user_input_lower):
            complexity = "simple"
        elif re.search(r"complex|advanced|comprehensive|detailed", user_input_lower):
            complexity = "complex"
        
        # Extract keywords
        keywords = re.findall(r'\b\w+\b', user_input_lower)
        keywords = [k for k in keywords if len(k) > 3]  # Filter short words
        
        return UserIntent(
            action=action,
            target=target,
            domain=domain,
            urgency=urgency,
            complexity=complexity,
            keywords=keywords
        )
    
    def recommend_tools(
        self,
        user_input: str,
        available_tools: List[ToolSpec],
        context: Optional[ToolContext] = None,
        max_recommendations: int = 5
    ) -> List[ToolRecommendation]:
        """Recommend tools based on user input and context."""
        
        # Parse user intent
        intent = self.parse_user_intent(user_input, context)
        
        # Get base recommendations from mappings
        base_tools = self._get_base_tool_recommendations(intent)
        
        # Score tools based on various factors
        tool_scores = {}
        available_tool_names = {tool.name for tool in available_tools}
        
        for tool_name in base_tools:
            if tool_name in available_tool_names:
                score = self._calculate_tool_score(
                    tool_name, intent, context, available_tools
                )
                tool_scores[tool_name] = score
        
        # Add context-based recommendations
        context_tools = self._get_context_based_recommendations(intent, context, available_tools)
        for tool_name, score in context_tools.items():
            if tool_name in available_tool_names:
                tool_scores[tool_name] = max(tool_scores.get(tool_name, 0), score)
        
        # Sort by score and create recommendations
        sorted_tools = sorted(tool_scores.items(), key=lambda x: x[1], reverse=True)
        
        recommendations = []
        for tool_name, score in sorted_tools[:max_recommendations]:
            tool_spec = next((t for t in available_tools if t.name == tool_name), None)
            if tool_spec:
                recommendation = ToolRecommendation(
                    tool_name=tool_name,
                    confidence=min(score, 1.0),
                    reasoning=self._generate_reasoning(tool_name, intent, context),
                    parameters=self._suggest_parameters(tool_spec, intent, context),
                    alternatives=self._get_alternatives(tool_name, sorted_tools)
                )
                recommendations.append(recommendation)
        
        return recommendations
    
    def _get_base_tool_recommendations(self, intent: UserIntent) -> List[str]:
        """Get base tool recommendations from mappings."""
        tools = []
        
        # Get tools for action + domain combination
        action_domain_tools = self._tool_mappings.get(intent.action, {}).get(intent.domain, [])
        tools.extend(action_domain_tools)
        
        # Get tools for action + target combination
        action_target_tools = self._tool_mappings.get(intent.action, {}).get(intent.target, [])
        tools.extend(action_target_tools)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_tools = []
        for tool in tools:
            if tool not in seen:
                seen.add(tool)
                unique_tools.append(tool)
        
        return unique_tools
    
    def _calculate_tool_score(
        self,
        tool_name: str,
        intent: UserIntent,
        context: Optional[ToolContext],
        available_tools: List[ToolSpec]
    ) -> float:
        """Calculate a score for a tool based on various factors."""
        score = 0.5  # Base score
        
        # Intent matching score
        intent_score = self._calculate_intent_score(tool_name, intent)
        score += intent_score * 0.3
        
        # Context relevance score
        if context:
            context_score = self._calculate_context_score(tool_name, context)
            score += context_score * 0.2
        
        # Historical usage score
        usage_score = self._calculate_usage_score(tool_name, intent)
        score += usage_score * 0.2
        
        # Tool capability score
        capability_score = self._calculate_capability_score(tool_name, intent, available_tools)
        score += capability_score * 0.3
        
        return min(score, 1.0)
    
    def _calculate_intent_score(self, tool_name: str, intent: UserIntent) -> float:
        """Calculate how well a tool matches the user intent."""
        # This is a simplified scoring system
        tool_intent_map = {
            "codebase": ["analyze", "search"],
            "advanced_codebase": ["analyze"],
            "file": ["create", "modify"],
            "python": ["create", "modify", "debug", "test"],
            "shell": ["debug", "test", "create"],
            "web": ["search"],
            "git": ["modify"]
        }
        
        if tool_name in tool_intent_map:
            if intent.action in tool_intent_map[tool_name]:
                return 1.0
            else:
                return 0.3
        
        return 0.5
    
    def _calculate_context_score(self, tool_name: str, context: ToolContext) -> float:
        """Calculate context relevance score."""
        score = 0.5
        
        # Project type relevance
        if context.project_type:
            project_tool_map = {
                "Python": ["python", "codebase"],
                "JavaScript": ["codebase", "file"],
                "Java": ["codebase", "file"],
                "React": ["codebase", "file", "web"]
            }
            
            if context.project_type in project_tool_map:
                if tool_name in project_tool_map[context.project_type]:
                    score += 0.3
        
        # Recent file context
        if context.related_files:
            # Prefer tools that work well with the file types in context
            pass
        
        return min(score, 1.0)
    
    def _calculate_usage_score(self, tool_name: str, intent: UserIntent) -> float:
        """Calculate score based on historical usage patterns."""
        # This would analyze historical usage data
        # For now, return a default score
        return 0.5
    
    def _calculate_capability_score(
        self, 
        tool_name: str, 
        intent: UserIntent, 
        available_tools: List[ToolSpec]
    ) -> float:
        """Calculate score based on tool capabilities."""
        tool_spec = next((t for t in available_tools if t.name == tool_name), None)
        if not tool_spec:
            return 0.0
        
        score = 0.5
        
        # Check if tool has required parameters for the intent
        if intent.complexity == "complex" and len(tool_spec.parameters) > 5:
            score += 0.2
        elif intent.complexity == "simple" and len(tool_spec.parameters) <= 3:
            score += 0.2
        
        return min(score, 1.0)
    
    def _get_context_based_recommendations(
        self,
        intent: UserIntent,
        context: Optional[ToolContext],
        available_tools: List[ToolSpec]
    ) -> Dict[str, float]:
        """Get additional recommendations based on context."""
        recommendations = {}
        
        if not context:
            return recommendations
        
        # Workspace-based recommendations
        if context.workspace_path:
            # Check for specific files that might indicate tool preferences
            workspace = Path(context.workspace_path)
            
            if (workspace / "requirements.txt").exists() or (workspace / "setup.py").exists():
                recommendations["python"] = 0.8
            
            if (workspace / "package.json").exists():
                recommendations["shell"] = 0.7
            
            if (workspace / ".git").exists():
                recommendations["git"] = 0.6
        
        return recommendations
    
    def _generate_reasoning(
        self, 
        tool_name: str, 
        intent: UserIntent, 
        context: Optional[ToolContext]
    ) -> str:
        """Generate reasoning for tool recommendation."""
        reasons = []
        
        # Intent-based reasoning
        if intent.action == "analyze" and tool_name in ["codebase", "advanced_codebase"]:
            reasons.append("Excellent for code analysis tasks")
        
        if intent.action == "create" and tool_name == "file":
            reasons.append("Best for creating new files")
        
        # Context-based reasoning
        if context and context.project_type:
            if context.project_type == "Python" and tool_name == "python":
                reasons.append("Matches Python project context")
        
        # Default reasoning
        if not reasons:
            reasons.append(f"Suitable for {intent.action} operations")
        
        return "; ".join(reasons)
    
    def _suggest_parameters(
        self, 
        tool_spec: ToolSpec, 
        intent: UserIntent, 
        context: Optional[ToolContext]
    ) -> Dict[str, Any]:
        """Suggest parameters for the tool based on intent and context."""
        suggestions = {}
        
        # Common parameter suggestions based on intent
        for param in tool_spec.parameters:
            if param.name == "action" and hasattr(param, 'enum'):
                # Suggest action based on intent
                action_map = {
                    "analyze": ["analyze", "index", "search"],
                    "search": ["search", "grep", "find"],
                    "create": ["create", "write"],
                    "modify": ["edit", "update"]
                }
                
                for action in action_map.get(intent.action, []):
                    if action in param.enum:
                        suggestions[param.name] = action
                        break
            
            elif param.name == "path" and context and context.workspace_path:
                suggestions[param.name] = str(context.workspace_path)
        
        return suggestions
    
    def _get_alternatives(self, tool_name: str, sorted_tools: List[Tuple[str, float]]) -> List[str]:
        """Get alternative tools."""
        alternatives = []
        for name, score in sorted_tools:
            if name != tool_name and len(alternatives) < 3:
                alternatives.append(name)
        return alternatives


__all__ = [
    "ContextAwareToolSelector",
    "ToolRecommendation",
    "UserIntent"
]
