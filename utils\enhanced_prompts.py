"""
Enhanced system prompts for AI agent with comprehensive tool calling capabilities.

This module provides sophisticated system prompts that enable the AI agent to
effectively use tools, understand large codebases, and provide intelligent assistance.
"""

import os
import json
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime

from tools.base import ToolSpec
from config import get_config


def create_enhanced_system_prompt(
    tools: List[ToolSpec],
    workspace_path: Optional[Path] = None,
    project_context: Optional[Dict[str, Any]] = None,
    user_preferences: Optional[Dict[str, Any]] = None
) -> str:
    """
    Create an enhanced system prompt with comprehensive tool calling instructions.
    
    Args:
        tools: Available tools
        workspace_path: Current workspace path
        project_context: Project-specific context information
        user_preferences: User preferences and settings
    
    Returns:
        Enhanced system prompt string
    """
    config = get_config()
    
    # Base identity and capabilities
    base_prompt = f"""# AI Agent - Advanced Coding Assistant

You are an advanced AI coding assistant with comprehensive tool access and deep understanding of software development. You excel at:

## Core Capabilities
- **Code Analysis & Understanding**: Deep comprehension of codebases, architectures, and patterns
- **Intelligent Tool Usage**: Strategic use of available tools to accomplish complex tasks
- **Project Management**: Understanding project structure, dependencies, and workflows
- **Problem Solving**: Breaking down complex problems into manageable steps
- **Code Generation**: Writing high-quality, maintainable code following best practices

## Current Context
- **Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Workspace**: {workspace_path or 'Not specified'}
- **Available Tools**: {len(tools)} tools loaded
- **Model**: {config.llm.model}

## Tool Usage Philosophy

### Strategic Tool Selection
- **Analyze First**: Use codebase analysis tools to understand project structure before making changes
- **Context Awareness**: Consider project type, language, and existing patterns when selecting tools
- **Efficiency**: Choose the most appropriate tool for each task - don't use complex tools for simple tasks
- **Safety**: Always validate and test changes, especially in large codebases

### Tool Calling Best Practices
1. **Read Before Write**: Always examine existing code before making modifications
2. **Incremental Changes**: Make small, focused changes rather than large rewrites
3. **Validation**: Test changes and verify they work as expected
4. **Documentation**: Update documentation and comments when modifying code
5. **Dependencies**: Consider impact on other parts of the codebase

## Large Codebase Expertise

### Navigation Strategies
- Use search and grep tools to find relevant code quickly
- Leverage dependency analysis to understand code relationships
- Identify key entry points and main workflows
- Map out architecture and design patterns

### Code Modification Approach
- Start with understanding the existing architecture
- Identify the minimal set of changes needed
- Consider backward compatibility and breaking changes
- Plan for testing and validation

### Performance Considerations
- Be mindful of tool execution time on large codebases
- Use targeted searches rather than broad scans when possible
- Cache analysis results when appropriate
- Prioritize critical paths and hot spots

"""

    # Add tool-specific instructions
    tool_instructions = create_tool_instructions(tools)
    
    # Add project-specific context
    project_instructions = create_project_context(project_context, workspace_path)
    
    # Add user preferences
    preference_instructions = create_preference_instructions(user_preferences)
    
    # Combine all sections
    full_prompt = base_prompt + tool_instructions + project_instructions + preference_instructions
    
    return full_prompt


def create_tool_instructions(tools: List[ToolSpec]) -> str:
    """Create detailed tool usage instructions."""
    if not tools:
        return "\n## No Tools Available\nOperating in text-only mode.\n"
    
    instructions = "\n## Available Tools\n\n"
    
    # Group tools by category
    tool_categories = {
        "Code Analysis": ["codebase", "search", "grep", "analyze"],
        "File Operations": ["file", "read", "write", "edit"],
        "Execution": ["shell", "python", "code", "run"],
        "Web & Research": ["web", "browser", "search_web"],
        "Version Control": ["git", "github", "commit"],
        "Project Management": ["rag", "memory", "workflow"],
        "System": ["computer", "process", "system"]
    }
    
    categorized_tools = {category: [] for category in tool_categories}
    uncategorized_tools = []
    
    for tool in tools:
        categorized = False
        for category, keywords in tool_categories.items():
            if any(keyword in tool.name.lower() for keyword in keywords):
                categorized_tools[category].append(tool)
                categorized = True
                break
        if not categorized:
            uncategorized_tools.append(tool)
    
    # Add categorized tools
    for category, category_tools in categorized_tools.items():
        if category_tools:
            instructions += f"### {category}\n"
            for tool in category_tools:
                instructions += f"- **{tool.name}**: {tool.description}\n"
                if tool.parameters:
                    key_params = [p for p in tool.parameters if p.required][:3]
                    if key_params:
                        param_list = ", ".join(f"{p.name}({p.type})" for p in key_params)
                        instructions += f"  - Key parameters: {param_list}\n"
            instructions += "\n"
    
    # Add uncategorized tools
    if uncategorized_tools:
        instructions += "### Other Tools\n"
        for tool in uncategorized_tools:
            instructions += f"- **{tool.name}**: {tool.description}\n"
        instructions += "\n"
    
    # Add tool usage guidelines
    instructions += """### Tool Usage Guidelines

#### Function Calling Format
When using tools, follow this format:
```
tool_name(parameter1="value1", parameter2="value2")
```

#### Tool Chaining
For complex tasks, chain tools logically:
1. **Analyze** → Use codebase tools to understand structure
2. **Plan** → Determine what changes are needed
3. **Execute** → Make changes using appropriate tools
4. **Validate** → Test and verify changes work

#### Error Handling
- If a tool fails, try alternative approaches
- Use simpler tools as fallbacks
- Always validate tool outputs before proceeding

#### Context Preservation
- Maintain awareness of previous tool outputs
- Build upon previous analysis results
- Reference earlier findings when making decisions

"""
    
    return instructions


def create_project_context(
    project_context: Optional[Dict[str, Any]],
    workspace_path: Optional[Path]
) -> str:
    """Create project-specific context instructions."""
    instructions = "\n## Project Context\n\n"
    
    if workspace_path and workspace_path.exists():
        instructions += f"**Working Directory**: `{workspace_path}`\n\n"
        
        # Detect project type
        project_type = detect_project_type(workspace_path)
        if project_type:
            instructions += f"**Detected Project Type**: {project_type}\n\n"
        
        # Add project-specific guidelines
        if project_type:
            instructions += get_project_type_guidelines(project_type)
    
    if project_context:
        instructions += "**Additional Context**:\n"
        for key, value in project_context.items():
            instructions += f"- {key}: {value}\n"
        instructions += "\n"
    
    return instructions


def detect_project_type(workspace_path: Path) -> Optional[str]:
    """Detect the type of project based on files present."""
    project_indicators = {
        "Python": ["requirements.txt", "setup.py", "pyproject.toml", "Pipfile"],
        "Node.js": ["package.json", "yarn.lock", "npm-shrinkwrap.json"],
        "React": ["package.json", "src/App.js", "src/App.tsx", "public/index.html"],
        "Vue.js": ["package.json", "vue.config.js", "src/main.js", "src/App.vue"],
        "Angular": ["package.json", "angular.json", "src/main.ts"],
        "Java": ["pom.xml", "build.gradle", "src/main/java"],
        "C#/.NET": ["*.csproj", "*.sln", "Program.cs"],
        "Go": ["go.mod", "main.go"],
        "Rust": ["Cargo.toml", "src/main.rs"],
        "PHP": ["composer.json", "index.php"],
        "Ruby": ["Gemfile", "config.ru", "app/"],
        "Django": ["manage.py", "settings.py", "requirements.txt"],
        "Flask": ["app.py", "requirements.txt"],
        "FastAPI": ["main.py", "requirements.txt"],
        "Docker": ["Dockerfile", "docker-compose.yml"],
        "Kubernetes": ["*.yaml", "*.yml", "kustomization.yaml"]
    }
    
    for project_type, indicators in project_indicators.items():
        for indicator in indicators:
            if "*" in indicator:
                # Handle glob patterns
                pattern = indicator.replace("*", "")
                if any(f.name.endswith(pattern) for f in workspace_path.rglob("*")):
                    return project_type
            else:
                if (workspace_path / indicator).exists():
                    return project_type
    
    return None


def get_project_type_guidelines(project_type: str) -> str:
    """Get project-type specific guidelines."""
    guidelines = {
        "Python": """
**Python Project Guidelines**:
- Follow PEP 8 style guidelines
- Use virtual environments for dependencies
- Consider type hints for better code quality
- Use pytest for testing
- Check for requirements.txt or pyproject.toml for dependencies
""",
        "Node.js": """
**Node.js Project Guidelines**:
- Follow npm/yarn conventions
- Check package.json for scripts and dependencies
- Use ESLint and Prettier for code quality
- Consider TypeScript for larger projects
- Respect .gitignore and .npmignore files
""",
        "React": """
**React Project Guidelines**:
- Follow React best practices and hooks patterns
- Use functional components over class components
- Consider component composition and reusability
- Check for existing state management (Redux, Context, etc.)
- Respect existing folder structure (components, hooks, utils)
""",
        "Java": """
**Java Project Guidelines**:
- Follow Java naming conventions
- Respect Maven/Gradle project structure
- Use appropriate design patterns
- Consider existing frameworks (Spring, etc.)
- Follow package organization conventions
""",
        "Go": """
**Go Project Guidelines**:
- Follow Go conventions and idioms
- Use go fmt for formatting
- Respect package organization
- Consider existing module structure
- Use go test for testing
"""
    }
    
    return guidelines.get(project_type, "")


def create_preference_instructions(user_preferences: Optional[Dict[str, Any]]) -> str:
    """Create user preference instructions."""
    if not user_preferences:
        return ""
    
    instructions = "\n## User Preferences\n\n"
    
    for key, value in user_preferences.items():
        instructions += f"- **{key}**: {value}\n"
    
    instructions += "\nAlways consider these preferences when making decisions and recommendations.\n"
    
    return instructions


def create_tool_context_prompt(
    tool_name: str,
    tool_spec: ToolSpec,
    context: Optional[Dict[str, Any]] = None
) -> str:
    """Create a context-aware prompt for a specific tool."""
    prompt = f"""
## Tool Context: {tool_name}

**Description**: {tool_spec.description}

**Purpose**: {tool_spec.description}

**Parameters**:
"""
    
    for param in tool_spec.parameters:
        required_str = "Required" if param.required else "Optional"
        default_str = f" (default: {param.default})" if param.default is not None else ""
        enum_str = f" (options: {', '.join(param.enum)})" if param.enum else ""
        
        prompt += f"- **{param.name}** ({param.type}, {required_str}): {param.description}{default_str}{enum_str}\n"
    
    if context:
        prompt += "\n**Current Context**:\n"
        for key, value in context.items():
            prompt += f"- {key}: {value}\n"
    
    prompt += f"""
**Best Practices for {tool_name}**:
- Validate inputs before execution
- Consider the impact on the broader codebase
- Use appropriate error handling
- Document significant changes
"""
    
    return prompt


__all__ = [
    "create_enhanced_system_prompt",
    "create_tool_instructions", 
    "create_project_context",
    "detect_project_type",
    "get_project_type_guidelines",
    "create_preference_instructions",
    "create_tool_context_prompt"
]
