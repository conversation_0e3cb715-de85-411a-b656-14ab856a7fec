#!/usr/bin/env python3
"""
Entry point for running the Advanced AI Agent as a module.

This allows running the agent with: python -m aiagent
"""

import sys
from pathlib import Path

# Add the parent directory to the path to allow imports
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from .aiagent.cli import main
except ImportError:
    # Fallback for development
    from cli import main

if __name__ == "__main__":
    main()
