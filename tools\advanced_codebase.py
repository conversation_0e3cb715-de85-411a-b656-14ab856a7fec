"""
Advanced codebase analysis tools for large projects.

This module provides sophisticated analysis capabilities including dependency mapping,
architecture analysis, code quality assessment, and intelligent navigation.
"""

import os
import re
import ast
import json
import logging
import hashlib
import networkx as nx
from typing import Dict, List, Any, Set, Optional, Tuple, Generator
from pathlib import Path
from collections import defaultdict, Counter
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, as_completed

try:
    import tree_sitter
    from tree_sitter import Language, Parser
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False

from tools.base import ToolSpec, Parameter
from message import Message

logger = logging.getLogger(__name__)


@dataclass
class CodeEntity:
    """Represents a code entity (function, class, module, etc.)."""
    name: str
    type: str  # function, class, module, variable
    file_path: str
    line_number: int
    end_line: Optional[int] = None
    signature: Optional[str] = None
    docstring: Optional[str] = None
    complexity: int = 0
    dependencies: List[str] = field(default_factory=list)
    dependents: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ArchitectureLayer:
    """Represents an architectural layer or component."""
    name: str
    description: str
    components: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    interfaces: List[str] = field(default_factory=list)
    patterns: List[str] = field(default_factory=list)


@dataclass
class CodebaseMetrics:
    """Comprehensive codebase metrics."""
    total_files: int = 0
    total_lines: int = 0
    code_lines: int = 0
    comment_lines: int = 0
    blank_lines: int = 0
    functions: int = 0
    classes: int = 0
    modules: int = 0
    complexity_score: float = 0.0
    maintainability_index: float = 0.0
    technical_debt_ratio: float = 0.0
    test_coverage: float = 0.0
    duplication_ratio: float = 0.0


class AdvancedCodebaseAnalyzer(ToolSpec):
    """Advanced codebase analysis tool for large projects."""
    
    def __init__(self):
        super().__init__(
            name="advanced_codebase",
            description="Advanced codebase analysis with dependency mapping, architecture analysis, and intelligent navigation",
            parameters=[
                Parameter(
                    name="action",
                    type="string",
                    description="Analysis action to perform",
                    required=True,
                    enum=[
                        "full_analysis", "dependency_map", "architecture", "hotspots",
                        "code_quality", "refactor_suggestions", "impact_analysis",
                        "test_coverage", "security_scan", "performance_analysis",
                        "documentation_gaps", "code_smells", "design_patterns"
                    ]
                ),
                Parameter(
                    name="path",
                    type="string",
                    description="Path to analyze (file or directory)",
                    required=False,
                    default="."
                ),
                Parameter(
                    name="target",
                    type="string",
                    description="Specific target for analysis (function, class, module)",
                    required=False
                ),
                Parameter(
                    name="depth",
                    type="integer",
                    description="Analysis depth (1-5, higher = more detailed)",
                    required=False,
                    default=3
                ),
                Parameter(
                    name="include_tests",
                    type="boolean",
                    description="Include test files in analysis",
                    required=False,
                    default=True
                ),
                Parameter(
                    name="output_format",
                    type="string",
                    description="Output format for results",
                    required=False,
                    default="detailed",
                    enum=["summary", "detailed", "json", "graph"]
                ),
                Parameter(
                    name="language_filter",
                    type="string",
                    description="Filter by programming language",
                    required=False,
                    enum=["python", "javascript", "typescript", "java", "cpp", "go", "rust"]
                )
            ],
            block_types=["advanced_codebase", "codebase", "analysis"]
        )
        
        self._entity_cache: Dict[str, CodeEntity] = {}
        self._dependency_graph: Optional[nx.DiGraph] = None
        self._architecture_layers: List[ArchitectureLayer] = []
        self._metrics_cache: Optional[CodebaseMetrics] = None
        
        # Language-specific analyzers
        self._language_analyzers = {
            "python": self._analyze_python_advanced,
            "javascript": self._analyze_javascript_advanced,
            "typescript": self._analyze_typescript_advanced,
            "java": self._analyze_java_advanced
        }
    
    def is_available(self) -> bool:
        """Check if advanced codebase analysis is available."""
        return True
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """Execute advanced codebase analysis."""
        try:
            params = self.validate_parameters(**kwargs)
            action = params["action"]
            
            # Route to appropriate analysis method
            if action == "full_analysis":
                yield from self._full_analysis(params)
            elif action == "dependency_map":
                yield from self._dependency_mapping(params)
            elif action == "architecture":
                yield from self._architecture_analysis(params)
            elif action == "hotspots":
                yield from self._identify_hotspots(params)
            elif action == "code_quality":
                yield from self._code_quality_analysis(params)
            elif action == "refactor_suggestions":
                yield from self._refactor_suggestions(params)
            elif action == "impact_analysis":
                yield from self._impact_analysis(params)
            elif action == "test_coverage":
                yield from self._test_coverage_analysis(params)
            elif action == "security_scan":
                yield from self._security_analysis(params)
            elif action == "performance_analysis":
                yield from self._performance_analysis(params)
            elif action == "documentation_gaps":
                yield from self._documentation_analysis(params)
            elif action == "code_smells":
                yield from self._code_smell_detection(params)
            elif action == "design_patterns":
                yield from self._design_pattern_analysis(params)
            else:
                yield self.create_response(f"Unknown analysis action: {action}")
                
        except Exception as e:
            logger.error(f"Error in advanced codebase analysis: {e}")
            yield self.create_response(self.format_error(e))
    
    def _full_analysis(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform comprehensive codebase analysis."""
        path = Path(params.get("path", "."))
        depth = params.get("depth", 3)
        
        yield self.create_response(f"🔍 Starting comprehensive analysis of: {path}")
        
        # Step 1: Index all code entities
        yield self.create_response("📊 Phase 1: Indexing code entities...")
        entities = self._index_code_entities(path, params)
        
        # Step 2: Build dependency graph
        yield self.create_response("🕸️ Phase 2: Building dependency graph...")
        self._build_dependency_graph(entities)
        
        # Step 3: Analyze architecture
        yield self.create_response("🏗️ Phase 3: Analyzing architecture...")
        self._analyze_architecture_layers(entities)
        
        # Step 4: Calculate metrics
        yield self.create_response("📈 Phase 4: Calculating metrics...")
        metrics = self._calculate_comprehensive_metrics(entities, path)
        
        # Step 5: Generate insights
        yield self.create_response("💡 Phase 5: Generating insights...")
        insights = self._generate_insights(entities, metrics)
        
        # Format comprehensive report
        report = self._format_full_analysis_report(entities, metrics, insights)
        yield self.create_response(report)
    
    def _index_code_entities(self, path: Path, params: Dict[str, Any]) -> List[CodeEntity]:
        """Index all code entities in the codebase."""
        entities = []
        language_filter = params.get("language_filter")
        include_tests = params.get("include_tests", True)
        
        # Get all relevant files
        files = self._get_code_files(path, language_filter, include_tests)
        
        # Use thread pool for parallel processing
        with ThreadPoolExecutor(max_workers=4) as executor:
            future_to_file = {
                executor.submit(self._analyze_file_entities, file_path): file_path
                for file_path in files
            }
            
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    file_entities = future.result()
                    entities.extend(file_entities)
                except Exception as e:
                    logger.warning(f"Error analyzing {file_path}: {e}")
        
        return entities
    
    def _analyze_file_entities(self, file_path: Path) -> List[CodeEntity]:
        """Analyze entities in a single file."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Determine language and use appropriate analyzer
            language = self._detect_language(file_path)
            analyzer = self._language_analyzers.get(language, self._analyze_generic)
            
            return analyzer(file_path, content)
            
        except Exception as e:
            logger.warning(f"Error analyzing file {file_path}: {e}")
            return []
    
    def _analyze_python_advanced(self, file_path: Path, content: str) -> List[CodeEntity]:
        """Advanced Python file analysis."""
        entities = []
        
        try:
            tree = ast.parse(content)
            
            # Analyze classes
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    entity = CodeEntity(
                        name=node.name,
                        type="class",
                        file_path=str(file_path),
                        line_number=node.lineno,
                        end_line=getattr(node, 'end_lineno', None),
                        docstring=ast.get_docstring(node),
                        complexity=self._calculate_complexity(node),
                        metadata={
                            "methods": [m.name for m in node.body if isinstance(m, ast.FunctionDef)],
                            "bases": [self._get_name(base) for base in node.bases],
                            "decorators": [self._get_name(dec) for dec in node.decorator_list]
                        }
                    )
                    entities.append(entity)
                
                elif isinstance(node, ast.FunctionDef):
                    entity = CodeEntity(
                        name=node.name,
                        type="function",
                        file_path=str(file_path),
                        line_number=node.lineno,
                        end_line=getattr(node, 'end_lineno', None),
                        signature=self._get_function_signature(node),
                        docstring=ast.get_docstring(node),
                        complexity=self._calculate_complexity(node),
                        metadata={
                            "args": [arg.arg for arg in node.args.args],
                            "decorators": [self._get_name(dec) for dec in node.decorator_list],
                            "returns": self._get_name(node.returns) if node.returns else None
                        }
                    )
                    entities.append(entity)
            
            # Analyze imports for dependencies
            for node in ast.walk(tree):
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    # Add import dependencies to relevant entities
                    pass
            
        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")
        
        return entities
    
    def _analyze_javascript_advanced(self, file_path: Path, content: str) -> List[CodeEntity]:
        """Advanced JavaScript/TypeScript file analysis."""
        entities = []
        
        # Function patterns
        function_patterns = [
            (r'function\s+(\w+)\s*\([^)]*\)\s*{', 'function'),
            (r'const\s+(\w+)\s*=\s*\([^)]*\)\s*=>', 'function'),
            (r'(\w+)\s*:\s*function\s*\([^)]*\)\s*{', 'method'),
            (r'class\s+(\w+)', 'class')
        ]
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            for pattern, entity_type in function_patterns:
                match = re.search(pattern, line)
                if match:
                    entity = CodeEntity(
                        name=match.group(1),
                        type=entity_type,
                        file_path=str(file_path),
                        line_number=i,
                        signature=line.strip(),
                        complexity=self._estimate_js_complexity(content, match.group(1))
                    )
                    entities.append(entity)
        
        return entities
    
    def _analyze_typescript_advanced(self, file_path: Path, content: str) -> List[CodeEntity]:
        """Advanced TypeScript file analysis."""
        # Similar to JavaScript but with type information
        return self._analyze_javascript_advanced(file_path, content)
    
    def _analyze_java_advanced(self, file_path: Path, content: str) -> List[CodeEntity]:
        """Advanced Java file analysis."""
        entities = []
        
        # Java class and method patterns
        class_pattern = r'(?:public|private|protected)?\s*class\s+(\w+)'
        method_pattern = r'(?:public|private|protected)?\s*(?:static\s+)?(?:\w+\s+)*(\w+)\s*\([^)]*\)\s*{'
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            # Check for class
            class_match = re.search(class_pattern, line)
            if class_match:
                entity = CodeEntity(
                    name=class_match.group(1),
                    type="class",
                    file_path=str(file_path),
                    line_number=i,
                    signature=line.strip()
                )
                entities.append(entity)
            
            # Check for method
            method_match = re.search(method_pattern, line)
            if method_match:
                entity = CodeEntity(
                    name=method_match.group(1),
                    type="method",
                    file_path=str(file_path),
                    line_number=i,
                    signature=line.strip()
                )
                entities.append(entity)
        
        return entities
    
    def _analyze_generic(self, file_path: Path, content: str) -> List[CodeEntity]:
        """Generic file analysis for unsupported languages."""
        # Basic pattern matching for common constructs
        entities = []
        
        # Common function patterns across languages
        patterns = [
            (r'def\s+(\w+)', 'function'),  # Python
            (r'function\s+(\w+)', 'function'),  # JavaScript
            (r'class\s+(\w+)', 'class'),  # Multiple languages
            (r'interface\s+(\w+)', 'interface'),  # TypeScript, Java
        ]
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            for pattern, entity_type in patterns:
                match = re.search(pattern, line)
                if match:
                    entity = CodeEntity(
                        name=match.group(1),
                        type=entity_type,
                        file_path=str(file_path),
                        line_number=i,
                        signature=line.strip()
                    )
                    entities.append(entity)
        
        return entities
    
    def _get_code_files(
        self, 
        path: Path, 
        language_filter: Optional[str] = None,
        include_tests: bool = True
    ) -> List[Path]:
        """Get all code files for analysis."""
        extensions = {
            "python": [".py", ".pyx", ".pyi"],
            "javascript": [".js", ".jsx", ".mjs"],
            "typescript": [".ts", ".tsx"],
            "java": [".java"],
            "cpp": [".cpp", ".cxx", ".cc", ".hpp", ".h"],
            "go": [".go"],
            "rust": [".rs"]
        }
        
        if language_filter:
            target_extensions = extensions.get(language_filter, [])
        else:
            target_extensions = []
            for exts in extensions.values():
                target_extensions.extend(exts)
        
        files = []
        exclude_patterns = [
            ".git", "node_modules", "__pycache__", ".venv", "venv",
            "dist", "build", ".pytest_cache", "target"
        ]
        
        if not include_tests:
            exclude_patterns.extend(["test", "tests", "spec", "__tests__"])
        
        for root, dirs, filenames in os.walk(path):
            # Filter out excluded directories
            dirs[:] = [d for d in dirs if not any(pattern in d for pattern in exclude_patterns)]
            
            for filename in filenames:
                file_path = Path(root) / filename
                if file_path.suffix in target_extensions:
                    if include_tests or not any(pattern in str(file_path).lower() for pattern in ["test", "spec"]):
                        files.append(file_path)
        
        return files
    
    def _detect_language(self, file_path: Path) -> str:
        """Detect programming language from file extension."""
        extension_map = {
            ".py": "python", ".pyx": "python", ".pyi": "python",
            ".js": "javascript", ".jsx": "javascript", ".mjs": "javascript",
            ".ts": "typescript", ".tsx": "typescript",
            ".java": "java",
            ".cpp": "cpp", ".cxx": "cpp", ".cc": "cpp", ".hpp": "cpp", ".h": "cpp",
            ".go": "go",
            ".rs": "rust"
        }
        
        return extension_map.get(file_path.suffix.lower(), "unknown")
    
    def _calculate_complexity(self, node: ast.AST) -> int:
        """Calculate cyclomatic complexity of an AST node."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def _estimate_js_complexity(self, content: str, function_name: str) -> int:
        """Estimate complexity for JavaScript functions."""
        # Simple heuristic based on control flow keywords
        complexity_keywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', '&&', '||']
        
        # Find function body (simplified)
        lines = content.split('\n')
        complexity = 1
        
        for line in lines:
            if function_name in line:
                for keyword in complexity_keywords:
                    complexity += line.count(keyword)
        
        return max(complexity, 1)
    
    def _get_name(self, node: ast.AST) -> str:
        """Get name from AST node."""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return f"{self._get_name(node.value)}.{node.attr}"
        elif isinstance(node, ast.Constant):
            return str(node.value)
        else:
            return str(node)
    
    def _get_function_signature(self, node: ast.FunctionDef) -> str:
        """Get function signature from AST node."""
        args = []
        for arg in node.args.args:
            arg_str = arg.arg
            if arg.annotation:
                arg_str += f": {self._get_name(arg.annotation)}"
            args.append(arg_str)
        
        signature = f"def {node.name}({', '.join(args)})"
        if node.returns:
            signature += f" -> {self._get_name(node.returns)}"
        
        return signature


    def _build_dependency_graph(self, entities: List[CodeEntity]) -> None:
        """Build dependency graph from code entities."""
        self._dependency_graph = nx.DiGraph()

        # Add nodes
        for entity in entities:
            self._dependency_graph.add_node(
                f"{entity.file_path}:{entity.name}",
                entity=entity
            )

        # Add edges based on dependencies
        for entity in entities:
            entity_id = f"{entity.file_path}:{entity.name}"
            for dep in entity.dependencies:
                if self._dependency_graph.has_node(dep):
                    self._dependency_graph.add_edge(entity_id, dep)

    def _analyze_architecture_layers(self, entities: List[CodeEntity]) -> None:
        """Analyze architectural layers and patterns."""
        # Group entities by directory structure
        layer_groups = defaultdict(list)

        for entity in entities:
            path_parts = Path(entity.file_path).parts
            if len(path_parts) > 1:
                layer = path_parts[-2]  # Parent directory
                layer_groups[layer].append(entity)

        # Create architecture layers
        self._architecture_layers = []
        for layer_name, layer_entities in layer_groups.items():
            layer = ArchitectureLayer(
                name=layer_name,
                description=f"Layer containing {len(layer_entities)} entities",
                components=[e.name for e in layer_entities]
            )
            self._architecture_layers.append(layer)

    def _calculate_comprehensive_metrics(self, entities: List[CodeEntity], path: Path) -> CodebaseMetrics:
        """Calculate comprehensive codebase metrics."""
        metrics = CodebaseMetrics()

        # Count entities
        metrics.functions = sum(1 for e in entities if e.type == "function")
        metrics.classes = sum(1 for e in entities if e.type == "class")
        metrics.modules = len(set(e.file_path for e in entities))

        # Calculate complexity
        total_complexity = sum(e.complexity for e in entities)
        metrics.complexity_score = total_complexity / max(len(entities), 1)

        # Calculate maintainability index (simplified)
        metrics.maintainability_index = max(0, 171 - 5.2 * metrics.complexity_score)

        return metrics

    def _generate_insights(self, entities: List[CodeEntity], metrics: CodebaseMetrics) -> Dict[str, Any]:
        """Generate insights from analysis."""
        insights = {
            "hotspots": [],
            "refactor_candidates": [],
            "architecture_issues": [],
            "quality_concerns": []
        }

        # Identify hotspots (high complexity entities)
        high_complexity = [e for e in entities if e.complexity > 10]
        insights["hotspots"] = [
            {
                "name": e.name,
                "file": e.file_path,
                "complexity": e.complexity,
                "type": e.type
            }
            for e in sorted(high_complexity, key=lambda x: x.complexity, reverse=True)[:10]
        ]

        # Identify refactor candidates
        large_functions = [e for e in entities if e.type == "function" and e.complexity > 15]
        insights["refactor_candidates"] = [
            {
                "name": e.name,
                "file": e.file_path,
                "reason": f"High complexity ({e.complexity})",
                "suggestion": "Consider breaking into smaller functions"
            }
            for e in large_functions
        ]

        return insights

    def _format_full_analysis_report(
        self,
        entities: List[CodeEntity],
        metrics: CodebaseMetrics,
        insights: Dict[str, Any]
    ) -> str:
        """Format comprehensive analysis report."""
        report = f"""
🔍 **Comprehensive Codebase Analysis Report**

## 📊 Overview
- **Total Entities**: {len(entities)}
- **Functions**: {metrics.functions}
- **Classes**: {metrics.classes}
- **Modules**: {metrics.modules}
- **Average Complexity**: {metrics.complexity_score:.2f}
- **Maintainability Index**: {metrics.maintainability_index:.1f}/100

## 🔥 Hotspots (High Complexity)
"""

        for hotspot in insights["hotspots"][:5]:
            report += f"- **{hotspot['name']}** ({hotspot['type']}) - Complexity: {hotspot['complexity']}\n"
            report += f"  📁 {hotspot['file']}\n"

        report += "\n## 🔄 Refactor Suggestions\n"
        for candidate in insights["refactor_candidates"][:5]:
            report += f"- **{candidate['name']}**: {candidate['reason']}\n"
            report += f"  💡 {candidate['suggestion']}\n"
            report += f"  📁 {candidate['file']}\n"

        report += f"\n## 🏗️ Architecture Layers\n"
        for layer in self._architecture_layers[:10]:
            report += f"- **{layer.name}**: {len(layer.components)} components\n"

        return report

    def _dependency_mapping(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Create dependency mapping visualization."""
        yield self.create_response("🕸️ Creating dependency map...")
        # Implementation for dependency mapping
        yield self.create_response("Dependency mapping completed")

    def _architecture_analysis(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Analyze software architecture."""
        yield self.create_response("🏗️ Analyzing architecture...")
        # Implementation for architecture analysis
        yield self.create_response("Architecture analysis completed")

    def _identify_hotspots(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Identify code hotspots."""
        yield self.create_response("🔥 Identifying hotspots...")
        # Implementation for hotspot identification
        yield self.create_response("Hotspot identification completed")

    def _code_quality_analysis(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Analyze code quality."""
        yield self.create_response("📊 Analyzing code quality...")
        # Implementation for code quality analysis
        yield self.create_response("Code quality analysis completed")

    def _refactor_suggestions(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Generate refactoring suggestions."""
        yield self.create_response("💡 Generating refactor suggestions...")
        # Implementation for refactor suggestions
        yield self.create_response("Refactor suggestions generated")

    def _impact_analysis(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform impact analysis."""
        yield self.create_response("🎯 Performing impact analysis...")
        # Implementation for impact analysis
        yield self.create_response("Impact analysis completed")

    def _test_coverage_analysis(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Analyze test coverage."""
        yield self.create_response("🧪 Analyzing test coverage...")
        # Implementation for test coverage analysis
        yield self.create_response("Test coverage analysis completed")

    def _security_analysis(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform security analysis."""
        yield self.create_response("🔒 Performing security scan...")
        # Implementation for security analysis
        yield self.create_response("Security analysis completed")

    def _performance_analysis(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Analyze performance characteristics."""
        yield self.create_response("⚡ Analyzing performance...")
        # Implementation for performance analysis
        yield self.create_response("Performance analysis completed")

    def _documentation_analysis(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Analyze documentation gaps."""
        yield self.create_response("📚 Analyzing documentation...")
        # Implementation for documentation analysis
        yield self.create_response("Documentation analysis completed")

    def _code_smell_detection(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Detect code smells."""
        yield self.create_response("👃 Detecting code smells...")
        # Implementation for code smell detection
        yield self.create_response("Code smell detection completed")

    def _design_pattern_analysis(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Analyze design patterns."""
        yield self.create_response("🎨 Analyzing design patterns...")
        # Implementation for design pattern analysis
        yield self.create_response("Design pattern analysis completed")


# Create tool instance
advanced_codebase_analyzer = AdvancedCodebaseAnalyzer()


__all__ = [
    "AdvancedCodebaseAnalyzer",
    "CodeEntity",
    "ArchitectureLayer",
    "CodebaseMetrics",
    "advanced_codebase_analyzer"
]
