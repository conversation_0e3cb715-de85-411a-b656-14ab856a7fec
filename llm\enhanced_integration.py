"""
Enhanced LLM integration with advanced tool calling and context management.

This module provides sophisticated LLM integration with better tool descriptions,
context injection, response handling, and support for large codebases.
"""

import json
import logging
from typing import Dict, List, Any, Optional, Generator, Union
from dataclasses import dataclass, field
from pathlib import Path

from message import Message
from tools.base import ToolSpec
from tools.enhanced_registry import <PERSON>hanced<PERSON><PERSON>Registry, ToolContext
from tools.context_aware_selection import ContextAwareToolSelector, ToolRecommendation
from tools.orchestration_engine import WorkflowOrchestrator
from utils.enhanced_prompts import create_enhanced_system_prompt, create_tool_context_prompt
from config import get_config

logger = logging.getLogger(__name__)


@dataclass
class LLMContext:
    """Enhanced context for LLM interactions."""
    workspace_path: Optional[Path] = None
    project_type: Optional[str] = None
    current_files: List[str] = field(default_factory=list)
    recent_operations: List[str] = field(default_factory=list)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    conversation_history: List[Message] = field(default_factory=list)
    active_tools: List[str] = field(default_factory=list)
    performance_metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EnhancedToolCall:
    """Enhanced tool call with context and metadata."""
    tool_name: str
    parameters: Dict[str, Any]
    context: Optional[ToolContext] = None
    confidence: float = 1.0
    reasoning: str = ""
    alternatives: List[str] = field(default_factory=list)
    expected_output: Optional[str] = None


class EnhancedLLMIntegration:
    """Enhanced LLM integration with advanced capabilities."""
    
    def __init__(self, registry: EnhancedToolRegistry):
        self.registry = registry
        self.selector = ContextAwareToolSelector()
        self.orchestrator = WorkflowOrchestrator(registry)
        self.config = get_config()
        self._context_cache: Dict[str, LLMContext] = {}
        
    def create_enhanced_system_message(
        self,
        available_tools: List[ToolSpec],
        context: Optional[LLMContext] = None
    ) -> Message:
        """Create an enhanced system message with comprehensive tool information."""
        
        # Convert LLMContext to ToolContext if needed
        tool_context = None
        if context:
            tool_context = ToolContext(
                workspace_path=context.workspace_path,
                project_type=context.project_type,
                user_preferences=context.user_preferences,
                related_files=context.current_files
            )
        
        # Create enhanced system prompt
        system_prompt = create_enhanced_system_prompt(
            tools=available_tools,
            workspace_path=context.workspace_path if context else None,
            project_context={"type": context.project_type} if context and context.project_type else None,
            user_preferences=context.user_preferences if context else None
        )
        
        # Add tool function definitions
        tool_definitions = self.registry.export_enhanced_tool_definitions()
        
        # Add context-specific instructions
        if context:
            system_prompt += self._create_context_instructions(context)
        
        # Add performance insights
        system_prompt += self._create_performance_insights(available_tools)
        
        return Message(
            role="system",
            content=system_prompt,
            metadata={
                "tool_definitions": tool_definitions,
                "context": tool_context.__dict__ if tool_context else None,
                "enhanced": True
            }
        )
    
    def _create_context_instructions(self, context: LLMContext) -> str:
        """Create context-specific instructions."""
        instructions = "\n## Current Context\n\n"
        
        if context.workspace_path:
            instructions += f"**Working Directory**: `{context.workspace_path}`\n"
        
        if context.project_type:
            instructions += f"**Project Type**: {context.project_type}\n"
        
        if context.current_files:
            instructions += f"**Current Files**: {', '.join(context.current_files[:5])}\n"
            if len(context.current_files) > 5:
                instructions += f"... and {len(context.current_files) - 5} more\n"
        
        if context.recent_operations:
            instructions += f"**Recent Operations**: {', '.join(context.recent_operations[-3:])}\n"
        
        if context.active_tools:
            instructions += f"**Active Tools**: {', '.join(context.active_tools)}\n"
        
        instructions += "\n**Context-Aware Guidelines**:\n"
        instructions += "- Consider the current project type when selecting tools\n"
        instructions += "- Be aware of recently accessed files and operations\n"
        instructions += "- Leverage active tools for efficiency\n"
        instructions += "- Maintain consistency with established patterns\n\n"
        
        return instructions
    
    def _create_performance_insights(self, tools: List[ToolSpec]) -> str:
        """Create performance insights for tools."""
        insights = "\n## Tool Performance Insights\n\n"
        
        # Get performance metrics from registry
        performance_data = self.registry.get_tool_performance()
        
        if performance_data:
            insights += "**Tool Reliability**:\n"
            for tool_name, metrics in performance_data.items():
                if tool_name in [t.name for t in tools]:
                    success_rate = metrics.get('successful_executions', 0) / max(metrics.get('total_executions', 1), 1)
                    avg_duration = metrics.get('average_duration', 0)
                    
                    reliability = "🟢 High" if success_rate > 0.9 else "🟡 Medium" if success_rate > 0.7 else "🔴 Low"
                    speed = "⚡ Fast" if avg_duration < 1.0 else "🐌 Slow" if avg_duration > 5.0 else "⏱️ Normal"
                    
                    insights += f"- **{tool_name}**: {reliability} reliability, {speed} execution\n"
            
            insights += "\n**Recommendations**:\n"
            insights += "- Prefer tools with high reliability for critical operations\n"
            insights += "- Use fast tools for interactive tasks\n"
            insights += "- Consider alternatives for unreliable tools\n\n"
        
        return insights
    
    def suggest_tools_for_query(
        self,
        user_query: str,
        available_tools: List[ToolSpec],
        context: Optional[LLMContext] = None
    ) -> List[ToolRecommendation]:
        """Suggest appropriate tools for a user query."""
        
        # Convert to ToolContext
        tool_context = None
        if context:
            tool_context = ToolContext(
                workspace_path=context.workspace_path,
                project_type=context.project_type,
                user_preferences=context.user_preferences,
                related_files=context.current_files
            )
        
        # Get recommendations
        recommendations = self.selector.recommend_tools(
            user_input=user_query,
            available_tools=available_tools,
            context=tool_context
        )
        
        return recommendations
    
    def create_enhanced_tool_calls(
        self,
        user_query: str,
        available_tools: List[ToolSpec],
        context: Optional[LLMContext] = None
    ) -> List[EnhancedToolCall]:
        """Create enhanced tool calls with context and reasoning."""
        
        recommendations = self.suggest_tools_for_query(user_query, available_tools, context)
        
        enhanced_calls = []
        for rec in recommendations:
            # Convert to ToolContext
            tool_context = None
            if context:
                tool_context = ToolContext(
                    workspace_path=context.workspace_path,
                    project_type=context.project_type,
                    user_preferences=context.user_preferences,
                    related_files=context.current_files
                )
            
            enhanced_call = EnhancedToolCall(
                tool_name=rec.tool_name,
                parameters=rec.parameters,
                context=tool_context,
                confidence=rec.confidence,
                reasoning=rec.reasoning,
                alternatives=rec.alternatives
            )
            enhanced_calls.append(enhanced_call)
        
        return enhanced_calls
    
    def execute_enhanced_tool_call(
        self,
        tool_call: EnhancedToolCall,
        content: str = ""
    ) -> Generator[Message, None, None]:
        """Execute an enhanced tool call with context."""
        
        try:
            # Add reasoning message
            yield Message(
                role="system",
                content=f"🔧 Using {tool_call.tool_name}: {tool_call.reasoning}",
                metadata={
                    "tool_name": tool_call.tool_name,
                    "confidence": tool_call.confidence,
                    "reasoning": tool_call.reasoning
                }
            )
            
            # Execute tool with context
            results = self.registry.execute_tool_with_context(
                tool_name=tool_call.tool_name,
                content=content,
                context=tool_call.context,
                **tool_call.parameters
            )
            
            # Yield results with enhanced metadata
            for result in results:
                enhanced_result = Message(
                    role=result.role,
                    content=result.content,
                    metadata={
                        **result.metadata,
                        "enhanced_call": True,
                        "confidence": tool_call.confidence,
                        "alternatives": tool_call.alternatives
                    }
                )
                yield enhanced_result
                
        except Exception as e:
            # Suggest alternatives on failure
            error_msg = f"❌ Tool execution failed: {str(e)}"
            if tool_call.alternatives:
                error_msg += f"\n💡 Consider alternatives: {', '.join(tool_call.alternatives)}"
            
            yield Message(
                role="system",
                content=error_msg,
                metadata={
                    "error": True,
                    "tool_name": tool_call.tool_name,
                    "alternatives": tool_call.alternatives
                }
            )
    
    def create_workflow_from_query(
        self,
        user_query: str,
        context: Optional[LLMContext] = None
    ) -> Optional[Any]:  # Returns Workflow
        """Create a workflow from user query."""
        
        # Convert to ToolContext
        tool_context = None
        if context:
            tool_context = ToolContext(
                workspace_path=context.workspace_path,
                project_type=context.project_type,
                user_preferences=context.user_preferences,
                related_files=context.current_files
            )
        
        return self.orchestrator.create_workflow_from_intent(user_query, tool_context)
    
    def format_tool_response(
        self,
        response: Message,
        context: Optional[LLMContext] = None
    ) -> Message:
        """Format tool response with enhanced information."""
        
        # Add context-aware formatting
        formatted_content = response.content
        
        # Add file links for code references
        if context and context.workspace_path:
            formatted_content = self._add_file_links(formatted_content, context.workspace_path)
        
        # Add performance information
        if response.metadata.get("tool"):
            tool_name = response.metadata["tool"]
            performance = self.registry.get_tool_performance(tool_name)
            if performance:
                avg_duration = performance.get("average_duration", 0)
                if avg_duration > 0:
                    formatted_content += f"\n\n⏱️ *Execution time: {avg_duration:.2f}s*"
        
        return Message(
            role=response.role,
            content=formatted_content,
            metadata={
                **response.metadata,
                "formatted": True,
                "context_aware": True
            }
        )
    
    def _add_file_links(self, content: str, workspace_path: Path) -> str:
        """Add clickable file links to content."""
        import re
        
        # Pattern to match file paths
        file_pattern = r'([a-zA-Z0-9_/\\.-]+\.(py|js|ts|java|cpp|h|go|rs|php|rb))'
        
        def replace_with_link(match):
            file_path = match.group(1)
            full_path = workspace_path / file_path
            if full_path.exists():
                return f"[{file_path}](file://{full_path})"
            return file_path
        
        return re.sub(file_pattern, replace_with_link, content)
    
    def update_context(
        self,
        context_id: str,
        updates: Dict[str, Any]
    ) -> None:
        """Update LLM context with new information."""
        
        if context_id not in self._context_cache:
            self._context_cache[context_id] = LLMContext()
        
        context = self._context_cache[context_id]
        
        # Update fields
        for key, value in updates.items():
            if hasattr(context, key):
                setattr(context, key, value)
        
        # Update recent operations
        if "operation" in updates:
            context.recent_operations.append(updates["operation"])
            # Keep only last 10 operations
            context.recent_operations = context.recent_operations[-10:]
    
    def get_context(self, context_id: str) -> Optional[LLMContext]:
        """Get LLM context by ID."""
        return self._context_cache.get(context_id)
    
    def create_context_summary(self, context: LLMContext) -> str:
        """Create a summary of the current context."""
        summary = "## Current Session Context\n\n"
        
        if context.workspace_path:
            summary += f"**Workspace**: {context.workspace_path}\n"
        
        if context.project_type:
            summary += f"**Project Type**: {context.project_type}\n"
        
        if context.current_files:
            summary += f"**Active Files**: {len(context.current_files)} files\n"
        
        if context.recent_operations:
            summary += f"**Recent Operations**: {', '.join(context.recent_operations[-3:])}\n"
        
        if context.active_tools:
            summary += f"**Active Tools**: {', '.join(context.active_tools)}\n"
        
        # Add performance summary
        if context.performance_metrics:
            summary += f"**Performance**: {len(context.performance_metrics)} metrics tracked\n"
        
        return summary
    
    def optimize_for_large_codebase(self, context: LLMContext) -> Dict[str, Any]:
        """Optimize settings for large codebase operations."""
        optimizations = {
            "batch_size": 50,  # Process files in batches
            "parallel_execution": True,
            "cache_results": True,
            "incremental_analysis": True,
            "memory_efficient": True
        }
        
        # Adjust based on project size
        if context.workspace_path:
            try:
                file_count = len(list(context.workspace_path.rglob("*.py")))
                if file_count > 1000:
                    optimizations["batch_size"] = 25
                    optimizations["timeout"] = 300
                elif file_count > 100:
                    optimizations["batch_size"] = 50
                    optimizations["timeout"] = 120
            except:
                pass
        
        return optimizations


__all__ = [
    "EnhancedLLMIntegration",
    "LLMContext",
    "EnhancedToolCall"
]
