"""
Enhanced tool registry with advanced function calling capabilities.

This module provides sophisticated tool management with context injection,
parameter validation, tool chaining, and intelligent tool selection.
"""

import json
import logging
import inspect
import asyncio
from typing import Dict, List, Any, Optional, Callable, Union, Type
from dataclasses import dataclass, field
from pathlib import Path
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, TimeoutError

from tools.base import ToolSpec, Parameter
from tools.registry import ToolRegistry
from message import Message
from config import get_config

logger = logging.getLogger(__name__)


@dataclass
class ToolContext:
    """Context information for tool execution."""
    workspace_path: Optional[Path] = None
    project_type: Optional[str] = None
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    execution_history: List[Dict[str, Any]] = field(default_factory=list)
    current_task: Optional[str] = None
    related_files: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)


@dataclass
class ToolExecution:
    """Record of tool execution."""
    tool_name: str
    parameters: Dict[str, Any]
    start_time: datetime
    end_time: Optional[datetime] = None
    success: bool = False
    result: Optional[Any] = None
    error: Optional[str] = None
    context: Optional[ToolContext] = None


@dataclass
class ToolChain:
    """Represents a chain of tool executions."""
    name: str
    description: str
    steps: List[Dict[str, Any]]
    context_requirements: List[str] = field(default_factory=list)
    parallel_execution: bool = False


class EnhancedToolRegistry(ToolRegistry):
    """Enhanced tool registry with advanced capabilities."""
    
    def __init__(self):
        super().__init__()
        self._context: Optional[ToolContext] = None
        self._execution_history: List[ToolExecution] = []
        self._tool_chains: Dict[str, ToolChain] = {}
        self._tool_dependencies: Dict[str, List[str]] = {}
        self._tool_performance: Dict[str, Dict[str, float]] = {}
        self._executor = ThreadPoolExecutor(max_workers=4)
        
    def set_context(self, context: ToolContext) -> None:
        """Set the current execution context."""
        self._context = context
        logger.debug(f"Context updated: {context.current_task}")
    
    def get_context(self) -> Optional[ToolContext]:
        """Get the current execution context."""
        return self._context
    
    def register_tool_chain(self, chain: ToolChain) -> None:
        """Register a tool chain for complex operations."""
        self._tool_chains[chain.name] = chain
        logger.debug(f"Registered tool chain: {chain.name}")
    
    def get_tool_chain(self, name: str) -> Optional[ToolChain]:
        """Get a tool chain by name."""
        return self._tool_chains.get(name)
    
    def list_tool_chains(self) -> List[str]:
        """List all available tool chains."""
        return list(self._tool_chains.keys())
    
    def execute_tool_with_context(
        self,
        tool_name: str,
        content: str,
        context: Optional[ToolContext] = None,
        timeout: Optional[int] = None,
        **kwargs
    ) -> List[Message]:
        """
        Execute a tool with enhanced context and validation.
        
        Args:
            tool_name: Name of the tool to execute
            content: Tool input content
            context: Execution context
            timeout: Execution timeout in seconds
            **kwargs: Additional tool parameters
        
        Returns:
            List of response messages
        """
        tool = self.get_tool(tool_name)
        if not tool:
            raise ValueError(f"Tool '{tool_name}' not found")
        
        if not tool.is_available():
            raise RuntimeError(f"Tool '{tool_name}' is not available")
        
        # Use provided context or current context
        exec_context = context or self._context
        
        # Create execution record
        execution = ToolExecution(
            tool_name=tool_name,
            parameters=kwargs,
            start_time=datetime.now(),
            context=exec_context
        )
        
        try:
            # Validate parameters with context
            validated_params = self._validate_parameters_with_context(
                tool, kwargs, exec_context
            )
            
            # Inject context into tool execution
            if exec_context:
                validated_params['_context'] = exec_context
            
            # Execute tool with timeout
            if timeout:
                future = self._executor.submit(
                    self._execute_tool_safe, tool, content, validated_params
                )
                try:
                    results = future.result(timeout=timeout)
                except TimeoutError:
                    future.cancel()
                    raise RuntimeError(f"Tool '{tool_name}' execution timed out after {timeout}s")
            else:
                results = self._execute_tool_safe(tool, content, validated_params)
            
            # Update execution record
            execution.end_time = datetime.now()
            execution.success = True
            execution.result = results
            
            # Update performance metrics
            self._update_performance_metrics(tool_name, execution)
            
            # Add to history
            self._execution_history.append(execution)
            
            return results
            
        except Exception as e:
            execution.end_time = datetime.now()
            execution.error = str(e)
            self._execution_history.append(execution)
            logger.error(f"Tool execution failed: {tool_name} - {e}")
            raise
    
    def _execute_tool_safe(
        self, 
        tool: ToolSpec, 
        content: str, 
        params: Dict[str, Any]
    ) -> List[Message]:
        """Safely execute a tool and collect results."""
        results = []
        try:
            for message in tool.execute(content, **params):
                results.append(message)
        except Exception as e:
            error_msg = Message(
                role="system",
                content=f"Tool execution error: {str(e)}",
                metadata={"tool": tool.name, "error": True}
            )
            results.append(error_msg)
        return results
    
    def _validate_parameters_with_context(
        self,
        tool: ToolSpec,
        params: Dict[str, Any],
        context: Optional[ToolContext]
    ) -> Dict[str, Any]:
        """Validate parameters with context awareness."""
        validated = tool.validate_parameters(**params)
        
        # Add context-based validation
        if context:
            # Validate file paths are within workspace
            if context.workspace_path:
                for key, value in validated.items():
                    if isinstance(value, str) and ('path' in key.lower() or 'file' in key.lower()):
                        if value and not self._is_safe_path(value, context.workspace_path):
                            logger.warning(f"Path '{value}' is outside workspace")
            
            # Add project-specific validation
            if context.project_type:
                validated = self._apply_project_validation(validated, context.project_type)
        
        return validated
    
    def _is_safe_path(self, path: str, workspace: Path) -> bool:
        """Check if a path is safe (within workspace)."""
        try:
            full_path = Path(path).resolve()
            workspace_resolved = workspace.resolve()
            return str(full_path).startswith(str(workspace_resolved))
        except:
            return False
    
    def _apply_project_validation(
        self, 
        params: Dict[str, Any], 
        project_type: str
    ) -> Dict[str, Any]:
        """Apply project-type specific parameter validation."""
        # This can be extended with project-specific rules
        return params
    
    def _update_performance_metrics(self, tool_name: str, execution: ToolExecution) -> None:
        """Update performance metrics for a tool."""
        if tool_name not in self._tool_performance:
            self._tool_performance[tool_name] = {
                'total_executions': 0,
                'successful_executions': 0,
                'average_duration': 0.0,
                'total_duration': 0.0
            }
        
        metrics = self._tool_performance[tool_name]
        metrics['total_executions'] += 1
        
        if execution.success:
            metrics['successful_executions'] += 1
        
        if execution.end_time:
            duration = (execution.end_time - execution.start_time).total_seconds()
            metrics['total_duration'] += duration
            metrics['average_duration'] = metrics['total_duration'] / metrics['total_executions']
    
    def get_tool_performance(self, tool_name: Optional[str] = None) -> Dict[str, Any]:
        """Get performance metrics for tools."""
        if tool_name:
            return self._tool_performance.get(tool_name, {})
        return self._tool_performance.copy()
    
    def suggest_tools_for_task(self, task_description: str) -> List[str]:
        """Suggest appropriate tools for a given task."""
        suggestions = []
        task_lower = task_description.lower()
        
        # Keyword-based suggestions
        tool_keywords = {
            'codebase': ['analyze', 'search', 'find', 'code', 'function', 'class'],
            'file': ['read', 'write', 'edit', 'create', 'file'],
            'shell': ['run', 'execute', 'command', 'terminal', 'bash'],
            'python': ['python', 'script', 'py', 'execute'],
            'web': ['web', 'http', 'api', 'request', 'download'],
            'git': ['git', 'commit', 'push', 'pull', 'branch', 'version'],
        }
        
        for tool_name, keywords in tool_keywords.items():
            if any(keyword in task_lower for keyword in keywords):
                if self.get_tool(tool_name):
                    suggestions.append(tool_name)
        
        # Performance-based ranking
        suggestions.sort(key=lambda t: self._get_tool_score(t), reverse=True)
        
        return suggestions[:5]  # Return top 5 suggestions
    
    def _get_tool_score(self, tool_name: str) -> float:
        """Calculate a score for tool recommendation."""
        metrics = self._tool_performance.get(tool_name, {})
        if not metrics:
            return 0.5  # Default score for new tools
        
        success_rate = metrics.get('successful_executions', 0) / max(metrics.get('total_executions', 1), 1)
        avg_duration = metrics.get('average_duration', 1.0)
        
        # Score based on success rate and speed (lower duration is better)
        score = success_rate * (1.0 / max(avg_duration, 0.1))
        return min(score, 1.0)
    
    def execute_tool_chain(
        self,
        chain_name: str,
        initial_input: str,
        context: Optional[ToolContext] = None
    ) -> List[Message]:
        """Execute a predefined tool chain."""
        chain = self.get_tool_chain(chain_name)
        if not chain:
            raise ValueError(f"Tool chain '{chain_name}' not found")
        
        results = []
        current_input = initial_input
        exec_context = context or self._context
        
        for step in chain.steps:
            tool_name = step['tool']
            step_params = step.get('parameters', {})
            
            try:
                step_results = self.execute_tool_with_context(
                    tool_name=tool_name,
                    content=current_input,
                    context=exec_context,
                    **step_params
                )
                
                results.extend(step_results)
                
                # Use last result as input for next step
                if step_results:
                    current_input = step_results[-1].content
                    
            except Exception as e:
                error_msg = Message(
                    role="system",
                    content=f"Tool chain step failed: {tool_name} - {str(e)}",
                    metadata={"chain": chain_name, "step": tool_name, "error": True}
                )
                results.append(error_msg)
                break
        
        return results
    
    def get_execution_history(
        self, 
        tool_name: Optional[str] = None,
        limit: int = 50
    ) -> List[ToolExecution]:
        """Get execution history, optionally filtered by tool name."""
        history = self._execution_history
        
        if tool_name:
            history = [ex for ex in history if ex.tool_name == tool_name]
        
        return history[-limit:]
    
    def export_enhanced_tool_definitions(self) -> List[Dict[str, Any]]:
        """Export enhanced tool definitions for LLM function calling."""
        definitions = []
        
        for tool in self.get_available_tools():
            if not tool.is_runnable:
                continue
            
            # Get performance metrics
            metrics = self._tool_performance.get(tool.name, {})
            success_rate = 0.0
            if metrics.get('total_executions', 0) > 0:
                success_rate = metrics.get('successful_executions', 0) / metrics['total_executions']
            
            definition = {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    },
                    "metadata": {
                        "success_rate": success_rate,
                        "average_duration": metrics.get('average_duration', 0.0),
                        "block_types": tool.block_types,
                        "category": self._categorize_tool(tool.name)
                    }
                }
            }
            
            # Add parameters with enhanced descriptions
            for param in tool.parameters:
                param_def = {
                    "type": param.type,
                    "description": param.description
                }
                
                if param.enum:
                    param_def["enum"] = param.enum
                
                if param.default is not None:
                    param_def["default"] = param.default
                
                definition["function"]["parameters"]["properties"][param.name] = param_def
                
                if param.required:
                    definition["function"]["parameters"]["required"].append(param.name)
            
            definitions.append(definition)
        
        return definitions
    
    def _categorize_tool(self, tool_name: str) -> str:
        """Categorize a tool for better organization."""
        categories = {
            "analysis": ["codebase", "analyze", "search", "grep"],
            "file_ops": ["file", "read", "write", "edit"],
            "execution": ["shell", "python", "run", "execute"],
            "web": ["web", "browser", "http"],
            "vcs": ["git", "github", "commit"],
            "system": ["computer", "process", "system"]
        }
        
        tool_lower = tool_name.lower()
        for category, keywords in categories.items():
            if any(keyword in tool_lower for keyword in keywords):
                return category
        
        return "other"


# Predefined tool chains for common operations
COMMON_TOOL_CHAINS = [
    ToolChain(
        name="analyze_codebase",
        description="Comprehensive codebase analysis",
        steps=[
            {"tool": "codebase", "parameters": {"action": "index"}},
            {"tool": "codebase", "parameters": {"action": "analyze"}},
            {"tool": "codebase", "parameters": {"action": "dependencies"}}
        ]
    ),
    ToolChain(
        name="safe_code_change",
        description="Safely modify code with validation",
        steps=[
            {"tool": "codebase", "parameters": {"action": "search"}},
            {"tool": "file", "parameters": {"action": "read"}},
            {"tool": "file", "parameters": {"action": "write"}},
            {"tool": "python", "parameters": {"action": "test"}}
        ]
    )
]


def create_enhanced_registry() -> EnhancedToolRegistry:
    """Create and configure an enhanced tool registry."""
    registry = EnhancedToolRegistry()
    
    # Register common tool chains
    for chain in COMMON_TOOL_CHAINS:
        registry.register_tool_chain(chain)
    
    return registry


__all__ = [
    "EnhancedToolRegistry",
    "ToolContext",
    "ToolExecution", 
    "ToolChain",
    "create_enhanced_registry",
    "COMMON_TOOL_CHAINS"
]
