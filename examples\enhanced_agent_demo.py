"""
Enhanced AI Agent Demo

This example demonstrates the advanced capabilities of the enhanced AI agent
including tool calling, workflow automation, and large codebase support.
"""

import asyncio
import logging
from pathlib import Path

from enhanced_agent import create_enhanced_agent
from utils.console import console

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def demo_basic_usage():
    """Demonstrate basic enhanced agent usage."""
    console.print("🚀 [bold blue]Enhanced AI Agent Demo[/bold blue]")
    console.print("=" * 50)
    
    # Create agent for current directory
    agent = create_enhanced_agent(Path.cwd())
    
    console.print("\n📊 [bold green]Available Tools:[/bold green]")
    tools = agent.list_available_tools()
    for tool in tools[:10]:  # Show first 10 tools
        status = "✅" if tool["available"] else "❌"
        console.print(f"  {status} {tool['name']}: {tool['description'][:60]}...")
    
    console.print(f"\n🔧 [bold yellow]Total Tools Available:[/bold yellow] {len(tools)}")


async def demo_codebase_analysis():
    """Demonstrate codebase analysis capabilities."""
    console.print("\n🔍 [bold blue]Codebase Analysis Demo[/bold blue]")
    console.print("-" * 40)
    
    agent = create_enhanced_agent(Path.cwd())
    
    # Analyze current codebase
    console.print("Analyzing current codebase...")
    
    async for message in agent.analyze_codebase(depth=2):
        if message.role == "system":
            console.print(f"[dim]{message.content}[/dim]")
        else:
            console.print(message.content)


async def demo_tool_recommendations():
    """Demonstrate intelligent tool recommendations."""
    console.print("\n💡 [bold blue]Tool Recommendation Demo[/bold blue]")
    console.print("-" * 40)
    
    agent = create_enhanced_agent(Path.cwd())
    
    # Test queries
    test_queries = [
        "analyze the code structure",
        "find all Python functions",
        "create a new file",
        "run tests",
        "search for TODO comments"
    ]
    
    for query in test_queries:
        console.print(f"\n🔍 Query: [italic]{query}[/italic]")
        suggestions = agent.get_tool_suggestions(query)
        
        for i, tool_name in enumerate(suggestions, 1):
            console.print(f"  {i}. {tool_name}")


async def demo_workflow_automation():
    """Demonstrate workflow automation."""
    console.print("\n🔄 [bold blue]Workflow Automation Demo[/bold blue]")
    console.print("-" * 40)
    
    agent = create_enhanced_agent(Path.cwd())
    
    # Show available workflow templates
    templates = agent.get_workflow_templates()
    console.print("Available workflow templates:")
    
    for template in templates:
        console.print(f"  • {template['name']}: {template['description']}")


async def demo_context_awareness():
    """Demonstrate context-aware capabilities."""
    console.print("\n🧠 [bold blue]Context Awareness Demo[/bold blue]")
    console.print("-" * 40)
    
    agent = create_enhanced_agent(Path.cwd())
    
    # Show session summary
    summary = agent.get_session_summary()
    console.print("Current session context:")
    console.print(summary)


async def demo_large_codebase_optimization():
    """Demonstrate large codebase optimizations."""
    console.print("\n⚡ [bold blue]Large Codebase Optimization Demo[/bold blue]")
    console.print("-" * 40)
    
    agent = create_enhanced_agent(Path.cwd())
    
    # Get optimizations
    optimizations = agent.optimize_for_large_codebase()
    console.print("Optimizations for large codebase:")
    
    for key, value in optimizations.items():
        console.print(f"  • {key}: {value}")


async def demo_interactive_session():
    """Demonstrate interactive session capabilities."""
    console.print("\n💬 [bold blue]Interactive Session Demo[/bold blue]")
    console.print("-" * 40)
    
    agent = create_enhanced_agent(Path.cwd())
    
    # Simulate user interactions
    test_inputs = [
        "What files are in this project?",
        "Show me the project structure",
        "Find all Python classes",
        "Analyze code complexity"
    ]
    
    for user_input in test_inputs:
        console.print(f"\n👤 User: [italic]{user_input}[/italic]")
        console.print("🤖 Agent:")
        
        async for message in agent.process_user_input(user_input, auto_execute=True):
            if message.content:
                # Truncate long responses for demo
                content = message.content[:200]
                if len(message.content) > 200:
                    content += "..."
                console.print(f"  {content}")


async def demo_error_handling():
    """Demonstrate error handling and recovery."""
    console.print("\n🛡️ [bold blue]Error Handling Demo[/bold blue]")
    console.print("-" * 40)
    
    agent = create_enhanced_agent(Path.cwd())
    
    # Test with invalid tool
    console.print("Testing with invalid tool...")
    try:
        async for message in agent.execute_tool_by_name("nonexistent_tool", "test"):
            console.print(f"  {message.content}")
    except Exception as e:
        console.print(f"  ❌ Handled error: {e}")
    
    # Test with invalid parameters
    console.print("\nTesting with invalid parameters...")
    try:
        async for message in agent.execute_tool_by_name(
            "file", 
            parameters={"invalid_param": "value"}
        ):
            console.print(f"  {message.content}")
    except Exception as e:
        console.print(f"  ❌ Handled error: {e}")


async def main():
    """Run all demos."""
    try:
        await demo_basic_usage()
        await demo_codebase_analysis()
        await demo_tool_recommendations()
        await demo_workflow_automation()
        await demo_context_awareness()
        await demo_large_codebase_optimization()
        await demo_interactive_session()
        await demo_error_handling()
        
        console.print("\n🎉 [bold green]Demo completed successfully![/bold green]")
        
    except Exception as e:
        console.print(f"\n❌ [bold red]Demo failed:[/bold red] {e}")
        logger.exception("Demo failed")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())
