"""
Enhanced AI Agent with comprehensive tool calling and large codebase support.

This module integrates all enhanced components to provide a sophisticated AI agent
with advanced tool calling, context awareness, and intelligent workflow automation.
"""

import os
import logging
from typing import List, Optional, Dict, Any, Generator
from pathlib import Path
from datetime import datetime

from message import Message, create_user_message, create_system_message
from config import get_config
from conversation import ConversationManager

# Enhanced components
from tools.enhanced_registry import Enhanced<PERSON>oolRegistry, ToolContext, create_enhanced_registry
from tools.context_aware_selection import ContextAwareToolSelector
from tools.orchestration_engine import WorkflowOrchestrator
from tools.advanced_codebase import AdvancedCodebaseAnalyzer
from llm.enhanced_integration import EnhancedLLMIntegration, LLMContext
from utils.enhanced_prompts import create_enhanced_system_prompt, detect_project_type

logger = logging.getLogger(__name__)


class EnhancedAIAgent:
    """Enhanced AI Agent with advanced capabilities."""
    
    def __init__(self, workspace_path: Optional[Path] = None):
        """
        Initialize the enhanced AI agent.
        
        Args:
            workspace_path: Path to the workspace directory
        """
        self.config = get_config()
        self.workspace_path = workspace_path or Path.cwd()
        
        # Initialize enhanced components
        self.registry = create_enhanced_registry()
        self.selector = ContextAwareToolSelector()
        self.orchestrator = WorkflowOrchestrator(self.registry)
        self.llm_integration = EnhancedLLMIntegration(self.registry)
        
        # Initialize tools
        self._initialize_tools()
        
        # Set up context
        self.context = self._create_initial_context()
        self.registry.set_context(self.context)
        
        # Session management
        self.session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.conversation_manager = ConversationManager(self.session_id)
        
        logger.info(f"Enhanced AI Agent initialized for workspace: {self.workspace_path}")
    
    def _initialize_tools(self) -> None:
        """Initialize and register all available tools."""
        # Discover and register tools
        self.registry.discover_tools("tools")
        
        # Initialize specific enhanced tools
        advanced_analyzer = AdvancedCodebaseAnalyzer()
        self.registry.register_tool(advanced_analyzer)
        
        # Get enabled tools from config
        enabled_tools = self.config.tools.enabled
        initialized_tools = []
        
        for tool_name in enabled_tools:
            tool = self.registry.get_tool(tool_name)
            if tool and tool.is_available():
                initialized_tool = self.registry.initialize_tool(tool_name)
                if initialized_tool:
                    initialized_tools.append(initialized_tool)
        
        logger.info(f"Initialized {len(initialized_tools)} tools: {[t.name for t in initialized_tools]}")
    
    def _create_initial_context(self) -> ToolContext:
        """Create initial tool context."""
        # Detect project type
        project_type = detect_project_type(self.workspace_path)
        
        # Create context
        context = ToolContext(
            workspace_path=self.workspace_path,
            project_type=project_type,
            current_task="initialization",
            user_preferences=self._load_user_preferences()
        )
        
        return context
    
    def _load_user_preferences(self) -> Dict[str, Any]:
        """Load user preferences from config."""
        return {
            "auto_confirm": self.config.tools.auto_confirm,
            "max_output_length": self.config.tools.max_output_length,
            "timeout": self.config.tools.timeout,
            "language_preference": "python",  # Default
            "verbosity": "detailed"
        }
    
    def process_user_input(
        self,
        user_input: str,
        stream: bool = True,
        auto_execute: bool = False
    ) -> Generator[Message, None, None]:
        """
        Process user input with enhanced capabilities.
        
        Args:
            user_input: User's input message
            stream: Whether to stream responses
            auto_execute: Whether to auto-execute tool recommendations
        
        Yields:
            Response messages
        """
        try:
            # Create user message
            user_msg = create_user_message(user_input)
            self.conversation_manager.add_message(user_msg)
            
            # Update LLM context
            llm_context = self._create_llm_context()
            
            # Check if this is a workflow request
            workflow = self.llm_integration.create_workflow_from_query(user_input, llm_context)
            
            if workflow:
                # Execute workflow
                yield create_system_message(f"🚀 Detected workflow request: {workflow.name}")
                
                for message in self.orchestrator.execute_workflow(workflow):
                    yield message
                    self.conversation_manager.add_message(message)
            else:
                # Get tool recommendations
                available_tools = self.registry.get_available_tools()
                recommendations = self.llm_integration.suggest_tools_for_query(
                    user_input, available_tools, llm_context
                )
                
                if recommendations and auto_execute:
                    # Auto-execute top recommendation
                    top_rec = recommendations[0]
                    if top_rec.confidence > 0.7:
                        enhanced_call = self.llm_integration.create_enhanced_tool_calls(
                            user_input, available_tools, llm_context
                        )[0]
                        
                        for message in self.llm_integration.execute_enhanced_tool_call(
                            enhanced_call, user_input
                        ):
                            formatted_msg = self.llm_integration.format_tool_response(
                                message, llm_context
                            )
                            yield formatted_msg
                            self.conversation_manager.add_message(formatted_msg)
                else:
                    # Provide recommendations
                    if recommendations:
                        rec_msg = self._format_recommendations(recommendations)
                        yield create_system_message(rec_msg)
                    else:
                        yield create_system_message(
                            "I can help you with various tasks. Try asking me to analyze code, "
                            "create files, search for patterns, or run commands."
                        )
            
            # Update context with recent operation
            self.context.execution_history.append({
                "timestamp": datetime.now(),
                "operation": user_input[:100],
                "type": "user_input"
            })
            
        except Exception as e:
            logger.error(f"Error processing user input: {e}")
            yield create_system_message(f"❌ Error: {str(e)}")
    
    def _create_llm_context(self) -> LLMContext:
        """Create LLM context from current state."""
        return LLMContext(
            workspace_path=self.workspace_path,
            project_type=self.context.project_type,
            current_files=self.context.related_files,
            recent_operations=[
                op.get("operation", "") for op in self.context.execution_history[-5:]
            ],
            user_preferences=self.context.user_preferences,
            conversation_history=self.conversation_manager.messages[-10:],
            active_tools=[tool.name for tool in self.registry.get_initialized_tools()],
            performance_metrics=self.registry.get_tool_performance()
        )
    
    def _format_recommendations(self, recommendations: List[Any]) -> str:
        """Format tool recommendations for display."""
        msg = "💡 **Tool Recommendations**:\n\n"
        
        for i, rec in enumerate(recommendations[:3], 1):
            confidence_emoji = "🟢" if rec.confidence > 0.8 else "🟡" if rec.confidence > 0.6 else "🔴"
            msg += f"{i}. {confidence_emoji} **{rec.tool_name}** (confidence: {rec.confidence:.1%})\n"
            msg += f"   {rec.reasoning}\n"
            
            if rec.parameters:
                key_params = list(rec.parameters.keys())[:3]
                if key_params:
                    msg += f"   Suggested parameters: {', '.join(key_params)}\n"
            
            if rec.alternatives:
                msg += f"   Alternatives: {', '.join(rec.alternatives[:2])}\n"
            msg += "\n"
        
        msg += "Would you like me to execute any of these tools?"
        return msg
    
    def execute_tool_by_name(
        self,
        tool_name: str,
        content: str = "",
        **parameters
    ) -> Generator[Message, None, None]:
        """Execute a specific tool by name."""
        try:
            results = self.registry.execute_tool_with_context(
                tool_name=tool_name,
                content=content,
                context=self.context,
                **parameters
            )
            
            for result in results:
                llm_context = self._create_llm_context()
                formatted_result = self.llm_integration.format_tool_response(result, llm_context)
                yield formatted_result
                self.conversation_manager.add_message(formatted_result)
                
        except Exception as e:
            error_msg = create_system_message(f"❌ Error executing {tool_name}: {str(e)}")
            yield error_msg
            self.conversation_manager.add_message(error_msg)
    
    def analyze_codebase(self, depth: int = 3) -> Generator[Message, None, None]:
        """Perform comprehensive codebase analysis."""
        yield create_system_message("🔍 Starting comprehensive codebase analysis...")
        
        # Use advanced codebase analyzer
        for message in self.execute_tool_by_name(
            "advanced_codebase",
            parameters={
                "action": "full_analysis",
                "path": str(self.workspace_path),
                "depth": depth
            }
        ):
            yield message
    
    def get_tool_suggestions(self, query: str) -> List[str]:
        """Get tool suggestions for a query."""
        available_tools = self.registry.get_available_tools()
        suggestions = self.registry.suggest_tools_for_task(query)
        return suggestions[:5]
    
    def get_session_summary(self) -> str:
        """Get a summary of the current session."""
        llm_context = self._create_llm_context()
        summary = self.llm_integration.create_context_summary(llm_context)
        
        # Add tool usage statistics
        performance = self.registry.get_tool_performance()
        if performance:
            summary += "\n## Tool Usage Statistics\n\n"
            for tool_name, metrics in performance.items():
                executions = metrics.get('total_executions', 0)
                success_rate = metrics.get('successful_executions', 0) / max(executions, 1)
                avg_duration = metrics.get('average_duration', 0)
                
                summary += f"- **{tool_name}**: {executions} executions, "
                summary += f"{success_rate:.1%} success rate, "
                summary += f"{avg_duration:.2f}s avg duration\n"
        
        return summary
    
    def save_session(self) -> None:
        """Save the current session."""
        self.conversation_manager.save()
        
        # Save context and performance data
        session_data = {
            "session_id": self.session_id,
            "workspace_path": str(self.workspace_path),
            "context": self.context.__dict__,
            "performance": self.registry.get_tool_performance(),
            "timestamp": datetime.now().isoformat()
        }
        
        session_file = self.workspace_path / ".aiagent" / f"{self.session_id}.json"
        session_file.parent.mkdir(exist_ok=True)
        
        import json
        with open(session_file, 'w') as f:
            json.dump(session_data, f, indent=2, default=str)
        
        logger.info(f"Session saved: {session_file}")
    
    def list_available_tools(self) -> List[Dict[str, Any]]:
        """List all available tools with their information."""
        tools_info = []
        
        for tool in self.registry.get_available_tools():
            performance = self.registry.get_tool_performance(tool.name)
            
            tool_info = {
                "name": tool.name,
                "description": tool.description,
                "parameters": len(tool.parameters),
                "block_types": tool.block_types,
                "available": tool.is_available(),
                "performance": performance
            }
            tools_info.append(tool_info)
        
        return tools_info
    
    def get_workflow_templates(self) -> List[Dict[str, str]]:
        """Get available workflow templates."""
        return self.orchestrator.list_workflow_templates()
    
    def optimize_for_large_codebase(self) -> Dict[str, Any]:
        """Optimize agent settings for large codebase operations."""
        llm_context = self._create_llm_context()
        optimizations = self.llm_integration.optimize_for_large_codebase(llm_context)
        
        # Apply optimizations to registry
        if optimizations.get("parallel_execution"):
            # Enable parallel execution in workflows
            pass
        
        if optimizations.get("cache_results"):
            # Enable result caching
            pass
        
        return optimizations


def create_enhanced_agent(workspace_path: Optional[Path] = None) -> EnhancedAIAgent:
    """Create and configure an enhanced AI agent."""
    return EnhancedAIAgent(workspace_path)


__all__ = [
    "EnhancedAIAgent",
    "create_enhanced_agent"
]
