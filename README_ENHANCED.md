# Enhanced AI Agent System

A sophisticated AI coding assistant with advanced tool calling capabilities, context awareness, and intelligent workflow automation designed for large codebase projects.

## 🚀 Key Features

### 🧠 Context-Aware Intelligence
- **Project Type Detection**: Automatically detects Python, JavaScript, Java, and other project types
- **Intent Recognition**: Understands natural language queries and maps them to appropriate tools
- **Historical Learning**: Learns from usage patterns to improve recommendations
- **Performance Optimization**: Adapts to large codebases with intelligent batching and caching

### 🔧 Advanced Tool System
- **Enhanced Tool Registry**: 50+ specialized tools with context injection
- **Intelligent Selection**: AI-powered tool recommendation with confidence scoring
- **Error Recovery**: Automatic fallback mechanisms and alternative suggestions
- **Performance Monitoring**: Real-time tool execution metrics and optimization

### 🏗️ Sophisticated Code Analysis
- **Dependency Mapping**: Visualize and analyze code dependencies
- **Architecture Analysis**: Identify design patterns and architectural layers
- **Quality Assessment**: Comprehensive code quality metrics and suggestions
- **Hotspot Detection**: Find complex or problematic code areas
- **Refactoring Recommendations**: AI-powered improvement suggestions

### 🔄 Workflow Automation
- **Predefined Workflows**: Common development task automation
- **Custom Workflows**: Create your own multi-step operations
- **Parallel Execution**: Run multiple tools simultaneously for efficiency
- **Progress Tracking**: Real-time workflow progress and status updates
- **Error Handling**: Intelligent error recovery and continuation strategies

## 📦 Installation

```bash
# Clone the repository
git clone <repository-url>
cd ai-agent

# Install dependencies
pip install -r requirements.txt

# Optional: Install enhanced dependencies
pip install tree-sitter networkx
```

## 🚀 Quick Start

### Basic Usage

```bash
# Run enhanced agent in interactive mode
python enhanced_main.py cli

# Quick codebase analysis
python enhanced_main.py analyze /path/to/project

# Run demo to see capabilities
python enhanced_main.py demo
```

### Interactive Session

```python
from enhanced_agent import create_enhanced_agent

# Create agent for current directory
agent = create_enhanced_agent()

# Process natural language queries
for message in agent.process_user_input("analyze the code structure"):
    print(message.content)
```

## 💡 Usage Examples

### Code Analysis
```bash
💬 You: analyze the codebase structure
🤖 Agent: 
  🔍 Starting comprehensive analysis...
  📊 Found 150 files, 45 classes, 320 functions
  🏗️ Detected layered architecture with MVC pattern
  ⚠️ Identified 3 hotspots requiring attention
```

### Intelligent Tool Selection
```bash
💬 You: find all TODO comments in Python files
🤖 Agent:
  🔧 Using codebase: Excellent for code search tasks
  📁 Searching 23 Python files...
  ✅ Found 12 TODO comments across 8 files
```

### Workflow Automation
```bash
💬 You: safely refactor the authentication module
🤖 Agent:
  🚀 Detected workflow request: Safe Code Modification
  📋 Step 1: Creating backup...
  🔍 Step 2: Analyzing target code...
  ✏️ Step 3: Applying modifications...
  ✅ Step 4: Validating changes...
  🧪 Step 5: Running tests...
```

## 🛠️ Available Tools

### Code Analysis Tools
- **codebase**: Basic codebase operations (search, index, analyze)
- **advanced_codebase**: Sophisticated analysis with AI insights
- **grep**: Pattern matching and text search
- **find**: File and directory discovery

### File Operations
- **file**: Enhanced file operations with safety checks
- **edit**: Context-aware code editing
- **backup**: Automatic backup creation

### Execution Tools
- **python**: Python code execution with environment management
- **shell**: Safe shell command execution
- **test**: Automated testing with coverage analysis

### Development Tools
- **git**: Version control integration
- **github**: GitHub API integration
- **dependencies**: Dependency analysis and management
- **documentation**: Automated documentation generation

### Web & Research
- **web**: Web scraping and API calls
- **browser**: Browser automation
- **search**: Web search capabilities

## 🔄 Predefined Workflows

### Code Analysis Workflow
1. **Index** → Catalog all code entities
2. **Analyze** → Perform comprehensive analysis
3. **Quality** → Assess code quality metrics
4. **Report** → Generate detailed insights

### Safe Modification Workflow
1. **Backup** → Create safety backup
2. **Analyze** → Understand target code
3. **Modify** → Apply changes safely
4. **Validate** → Check syntax and structure
5. **Test** → Run automated tests

### Project Setup Workflow
1. **Initialize** → Create project structure
2. **Dependencies** → Set up package management
3. **Configuration** → Configure development tools
4. **Documentation** → Generate initial docs

## ⚙️ Configuration

### Basic Configuration
```python
# config.py
tools = ToolsConfig(
    enabled=[
        "codebase", "advanced_codebase", "file", "python", 
        "shell", "git", "web", "browser"
    ],
    auto_confirm=False,
    timeout=300,
    max_output_length=50000
)
```

### Large Codebase Optimization
```python
# Automatic optimization for projects with 1000+ files
optimizations = agent.optimize_for_large_codebase()
# → Enables batching, caching, parallel execution
```

## 📊 Performance Features

### Intelligent Batching
- Processes large codebases in manageable chunks
- Adaptive batch sizes based on system resources
- Memory-efficient streaming for large files

### Caching System
- Caches analysis results for faster subsequent operations
- Incremental updates for modified files only
- Persistent cache across sessions

### Parallel Execution
- Runs independent operations simultaneously
- Intelligent dependency resolution
- Resource-aware scheduling

## 🔍 Advanced Analysis Capabilities

### Dependency Mapping
```python
# Visualize code dependencies
agent.execute_tool_by_name("advanced_codebase", 
    parameters={"action": "dependency_map"})
```

### Architecture Analysis
```python
# Analyze software architecture
agent.execute_tool_by_name("advanced_codebase",
    parameters={"action": "architecture"})
```

### Code Quality Assessment
```python
# Comprehensive quality analysis
agent.execute_tool_by_name("advanced_codebase",
    parameters={"action": "code_quality"})
```

## 🛡️ Error Handling & Recovery

### Automatic Retry Logic
- Exponential backoff for transient failures
- Configurable retry limits per tool
- Intelligent failure classification

### Fallback Mechanisms
- Alternative tool suggestions on failure
- Graceful degradation for partial results
- Context-aware error recovery

### Safety Features
- Workspace boundary enforcement
- Permission validation
- Backup creation for destructive operations

## 📈 Monitoring & Analytics

### Performance Metrics
- Tool execution times and success rates
- Resource usage monitoring
- Bottleneck identification

### Usage Analytics
- Tool usage patterns and preferences
- Workflow effectiveness metrics
- User interaction insights

### Session Management
- Persistent session state
- Conversation history
- Context preservation across restarts

## 🔧 Extending the System

### Adding New Tools
```python
from tools.base import ToolSpec, Parameter

class CustomTool(ToolSpec):
    def __init__(self):
        super().__init__(
            name="custom_tool",
            description="Custom tool description",
            parameters=[
                Parameter("input", "string", "Input parameter", required=True)
            ]
        )
    
    def is_available(self) -> bool:
        return True
    
    def execute(self, content: str, **kwargs):
        # Tool implementation
        yield self.create_response("Tool executed successfully")
```

### Creating Custom Workflows
```python
workflow = agent.orchestrator.create_custom_workflow(
    name="Custom Analysis",
    description="Custom analysis workflow",
    steps=[
        {
            "tool_name": "codebase",
            "parameters": {"action": "index"},
            "description": "Index codebase"
        },
        {
            "tool_name": "custom_tool",
            "parameters": {"input": "analysis_data"},
            "description": "Custom analysis",
            "dependencies": ["step_0"]
        }
    ]
)
```

## 📚 Documentation

- [Enhanced Features Guide](docs/ENHANCED_FEATURES.md)
- [API Reference](docs/API_REFERENCE.md)
- [Tool Development Guide](docs/TOOL_DEVELOPMENT.md)
- [Workflow Creation Guide](docs/WORKFLOW_GUIDE.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your enhancement
4. Add tests and documentation
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built on top of advanced LLM capabilities
- Inspired by VSCode Agent and other AI coding assistants
- Uses tree-sitter for advanced code parsing
- Leverages networkx for dependency analysis

---

**Enhanced AI Agent** - Intelligent coding assistance for the modern developer
