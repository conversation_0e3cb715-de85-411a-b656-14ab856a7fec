"""
Tool orchestration engine for complex multi-step operations.

This module provides workflow automation, tool chaining, parallel execution,
and intelligent error handling for complex development tasks.
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional, Callable, Union, Generator
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path

from tools.base import ToolSpec
from tools.enhanced_registry import <PERSON>hanced<PERSON><PERSON>R<PERSON><PERSON><PERSON>, ToolContext, ToolExecution
from tools.context_aware_selection import ContextAwareToolSelector, UserIntent
from message import Message

logger = logging.getLogger(__name__)


class WorkflowStatus(Enum):
    """Workflow execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class StepStatus(Enum):
    """Individual step status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class WorkflowStep:
    """Represents a single step in a workflow."""
    id: str
    tool_name: str
    description: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    input_content: str = ""
    dependencies: List[str] = field(default_factory=list)
    conditions: List[str] = field(default_factory=list)
    retry_count: int = 0
    max_retries: int = 3
    timeout: Optional[int] = None
    status: StepStatus = StepStatus.PENDING
    result: Optional[List[Message]] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


@dataclass
class Workflow:
    """Represents a complete workflow."""
    id: str
    name: str
    description: str
    steps: List[WorkflowStep] = field(default_factory=list)
    context: Optional[ToolContext] = None
    status: WorkflowStatus = WorkflowStatus.PENDING
    parallel_execution: bool = False
    continue_on_error: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class WorkflowOrchestrator:
    """Orchestrates complex tool workflows."""
    
    def __init__(self, registry: EnhancedToolRegistry):
        self.registry = registry
        self.selector = ContextAwareToolSelector()
        self._active_workflows: Dict[str, Workflow] = {}
        self._workflow_templates: Dict[str, Workflow] = {}
        self._executor = ThreadPoolExecutor(max_workers=8)
        self._load_predefined_workflows()
    
    def _load_predefined_workflows(self) -> None:
        """Load predefined workflow templates."""
        # Code analysis workflow
        analysis_workflow = Workflow(
            id="code_analysis",
            name="Comprehensive Code Analysis",
            description="Perform comprehensive analysis of a codebase",
            steps=[
                WorkflowStep(
                    id="index",
                    tool_name="codebase",
                    description="Index the codebase",
                    parameters={"action": "index"}
                ),
                WorkflowStep(
                    id="analyze",
                    tool_name="advanced_codebase",
                    description="Perform advanced analysis",
                    parameters={"action": "full_analysis"},
                    dependencies=["index"]
                ),
                WorkflowStep(
                    id="quality",
                    tool_name="advanced_codebase",
                    description="Analyze code quality",
                    parameters={"action": "code_quality"},
                    dependencies=["analyze"]
                )
            ]
        )
        self._workflow_templates["code_analysis"] = analysis_workflow
        
        # Safe code modification workflow
        safe_modify_workflow = Workflow(
            id="safe_modify",
            name="Safe Code Modification",
            description="Safely modify code with validation",
            steps=[
                WorkflowStep(
                    id="backup",
                    tool_name="file",
                    description="Create backup of target file",
                    parameters={"action": "copy"}
                ),
                WorkflowStep(
                    id="analyze_target",
                    tool_name="codebase",
                    description="Analyze target code",
                    parameters={"action": "search"},
                    dependencies=["backup"]
                ),
                WorkflowStep(
                    id="modify",
                    tool_name="file",
                    description="Modify the code",
                    parameters={"action": "write"},
                    dependencies=["analyze_target"]
                ),
                WorkflowStep(
                    id="validate",
                    tool_name="python",
                    description="Validate changes",
                    parameters={"action": "syntax_check"},
                    dependencies=["modify"]
                ),
                WorkflowStep(
                    id="test",
                    tool_name="python",
                    description="Run tests",
                    parameters={"action": "test"},
                    dependencies=["validate"]
                )
            ],
            continue_on_error=False
        )
        self._workflow_templates["safe_modify"] = safe_modify_workflow
    
    def create_workflow_from_intent(
        self,
        user_input: str,
        context: Optional[ToolContext] = None
    ) -> Optional[Workflow]:
        """Create a workflow based on user intent."""
        intent = self.selector.parse_user_intent(user_input, context)
        
        # Map intent to workflow templates
        intent_workflow_map = {
            ("analyze", "project"): "code_analysis",
            ("analyze", "code"): "code_analysis",
            ("modify", "code"): "safe_modify",
            ("create", "project"): "project_setup"
        }
        
        template_key = (intent.action, intent.target)
        template_id = intent_workflow_map.get(template_key)
        
        if template_id and template_id in self._workflow_templates:
            # Clone template and customize
            template = self._workflow_templates[template_id]
            workflow = self._clone_workflow(template)
            workflow.context = context
            
            # Customize based on specific intent
            self._customize_workflow(workflow, intent, user_input)
            
            return workflow
        
        return None
    
    def create_custom_workflow(
        self,
        name: str,
        description: str,
        steps: List[Dict[str, Any]],
        context: Optional[ToolContext] = None
    ) -> Workflow:
        """Create a custom workflow from step definitions."""
        workflow_steps = []
        
        for i, step_def in enumerate(steps):
            step = WorkflowStep(
                id=step_def.get("id", f"step_{i}"),
                tool_name=step_def["tool_name"],
                description=step_def.get("description", f"Step {i+1}"),
                parameters=step_def.get("parameters", {}),
                input_content=step_def.get("input_content", ""),
                dependencies=step_def.get("dependencies", []),
                conditions=step_def.get("conditions", []),
                max_retries=step_def.get("max_retries", 3),
                timeout=step_def.get("timeout")
            )
            workflow_steps.append(step)
        
        workflow = Workflow(
            id=f"custom_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            name=name,
            description=description,
            steps=workflow_steps,
            context=context
        )
        
        return workflow
    
    def execute_workflow(
        self,
        workflow: Workflow,
        progress_callback: Optional[Callable[[str, float], None]] = None
    ) -> Generator[Message, None, None]:
        """Execute a workflow and yield progress messages."""
        workflow.status = WorkflowStatus.RUNNING
        workflow.started_at = datetime.now()
        self._active_workflows[workflow.id] = workflow
        
        try:
            yield Message(
                role="system",
                content=f"🚀 Starting workflow: {workflow.name}",
                metadata={"workflow_id": workflow.id, "status": "started"}
            )
            
            if workflow.parallel_execution:
                yield from self._execute_parallel(workflow, progress_callback)
            else:
                yield from self._execute_sequential(workflow, progress_callback)
            
            workflow.status = WorkflowStatus.COMPLETED
            workflow.completed_at = datetime.now()
            
            yield Message(
                role="system",
                content=f"✅ Workflow completed: {workflow.name}",
                metadata={"workflow_id": workflow.id, "status": "completed"}
            )
            
        except Exception as e:
            workflow.status = WorkflowStatus.FAILED
            logger.error(f"Workflow {workflow.id} failed: {e}")
            
            yield Message(
                role="system",
                content=f"❌ Workflow failed: {workflow.name} - {str(e)}",
                metadata={"workflow_id": workflow.id, "status": "failed", "error": str(e)}
            )
        
        finally:
            if workflow.id in self._active_workflows:
                del self._active_workflows[workflow.id]
    
    def _execute_sequential(
        self,
        workflow: Workflow,
        progress_callback: Optional[Callable[[str, float], None]] = None
    ) -> Generator[Message, None, None]:
        """Execute workflow steps sequentially."""
        completed_steps = set()
        total_steps = len(workflow.steps)
        
        while len(completed_steps) < total_steps:
            # Find next executable step
            next_step = self._find_next_step(workflow.steps, completed_steps)
            
            if not next_step:
                # Check if we're stuck due to failed dependencies
                remaining_steps = [s for s in workflow.steps if s.id not in completed_steps]
                if remaining_steps:
                    failed_step = remaining_steps[0]
                    failed_step.status = StepStatus.FAILED
                    failed_step.error = "Dependencies not satisfied"
                    
                    if not workflow.continue_on_error:
                        raise RuntimeError(f"Step {failed_step.id} cannot be executed")
                break
            
            # Execute step
            yield from self._execute_step(next_step, workflow.context)
            
            if next_step.status == StepStatus.COMPLETED:
                completed_steps.add(next_step.id)
            elif next_step.status == StepStatus.FAILED and not workflow.continue_on_error:
                raise RuntimeError(f"Step {next_step.id} failed: {next_step.error}")
            
            # Update progress
            progress = len(completed_steps) / total_steps
            if progress_callback:
                progress_callback(f"Completed {len(completed_steps)}/{total_steps} steps", progress)
    
    def _execute_parallel(
        self,
        workflow: Workflow,
        progress_callback: Optional[Callable[[str, float], None]] = None
    ) -> Generator[Message, None, None]:
        """Execute workflow steps in parallel where possible."""
        completed_steps = set()
        running_steps = set()
        total_steps = len(workflow.steps)
        
        # Submit initial steps that have no dependencies
        futures = {}
        for step in workflow.steps:
            if not step.dependencies:
                future = self._executor.submit(self._execute_step_sync, step, workflow.context)
                futures[future] = step
                running_steps.add(step.id)
                step.status = StepStatus.RUNNING
        
        while futures or len(completed_steps) < total_steps:
            if futures:
                # Wait for at least one step to complete
                for future in as_completed(futures, timeout=1.0):
                    step = futures[future]
                    try:
                        result = future.result()
                        step.result = result
                        step.status = StepStatus.COMPLETED
                        completed_steps.add(step.id)
                        running_steps.discard(step.id)
                        
                        yield Message(
                            role="system",
                            content=f"✅ Step completed: {step.description}",
                            metadata={"step_id": step.id, "workflow_id": workflow.id}
                        )
                        
                    except Exception as e:
                        step.status = StepStatus.FAILED
                        step.error = str(e)
                        running_steps.discard(step.id)
                        
                        if not workflow.continue_on_error:
                            # Cancel remaining futures
                            for f in futures:
                                f.cancel()
                            raise RuntimeError(f"Step {step.id} failed: {str(e)}")
                    
                    del futures[future]
                    break
            
            # Submit new steps whose dependencies are now satisfied
            for step in workflow.steps:
                if (step.id not in completed_steps and 
                    step.id not in running_steps and
                    step.status == StepStatus.PENDING and
                    all(dep in completed_steps for dep in step.dependencies)):
                    
                    future = self._executor.submit(self._execute_step_sync, step, workflow.context)
                    futures[future] = step
                    running_steps.add(step.id)
                    step.status = StepStatus.RUNNING
            
            # Update progress
            progress = len(completed_steps) / total_steps
            if progress_callback:
                progress_callback(f"Completed {len(completed_steps)}/{total_steps} steps", progress)
    
    def _find_next_step(self, steps: List[WorkflowStep], completed: Set[str]) -> Optional[WorkflowStep]:
        """Find the next step that can be executed."""
        for step in steps:
            if (step.id not in completed and 
                step.status == StepStatus.PENDING and
                all(dep in completed for dep in step.dependencies)):
                return step
        return None
    
    def _execute_step(
        self,
        step: WorkflowStep,
        context: Optional[ToolContext]
    ) -> Generator[Message, None, None]:
        """Execute a single workflow step."""
        step.status = StepStatus.RUNNING
        step.start_time = datetime.now()
        
        yield Message(
            role="system",
            content=f"🔄 Executing: {step.description}",
            metadata={"step_id": step.id}
        )
        
        try:
            # Get the tool
            tool = self.registry.get_tool(step.tool_name)
            if not tool:
                raise RuntimeError(f"Tool '{step.tool_name}' not found")
            
            # Execute with timeout and retries
            for attempt in range(step.max_retries + 1):
                try:
                    results = self.registry.execute_tool_with_context(
                        tool_name=step.tool_name,
                        content=step.input_content,
                        context=context,
                        timeout=step.timeout,
                        **step.parameters
                    )
                    
                    step.result = results
                    step.status = StepStatus.COMPLETED
                    step.end_time = datetime.now()
                    
                    # Yield results
                    for result in results:
                        yield result
                    
                    break
                    
                except Exception as e:
                    step.retry_count = attempt
                    if attempt < step.max_retries:
                        yield Message(
                            role="system",
                            content=f"⚠️ Step failed, retrying ({attempt + 1}/{step.max_retries}): {str(e)}",
                            metadata={"step_id": step.id, "attempt": attempt + 1}
                        )
                    else:
                        step.status = StepStatus.FAILED
                        step.error = str(e)
                        step.end_time = datetime.now()
                        raise
                        
        except Exception as e:
            step.status = StepStatus.FAILED
            step.error = str(e)
            step.end_time = datetime.now()
            
            yield Message(
                role="system",
                content=f"❌ Step failed: {step.description} - {str(e)}",
                metadata={"step_id": step.id, "error": str(e)}
            )
    
    def _execute_step_sync(
        self,
        step: WorkflowStep,
        context: Optional[ToolContext]
    ) -> List[Message]:
        """Execute a step synchronously (for parallel execution)."""
        results = []
        for message in self._execute_step(step, context):
            results.append(message)
        return results
    
    def _clone_workflow(self, template: Workflow) -> Workflow:
        """Clone a workflow template."""
        import copy
        cloned = copy.deepcopy(template)
        cloned.id = f"{template.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        cloned.status = WorkflowStatus.PENDING
        cloned.created_at = datetime.now()
        return cloned
    
    def _customize_workflow(self, workflow: Workflow, intent: UserIntent, user_input: str) -> None:
        """Customize workflow based on user intent."""
        # Add path parameter if context has workspace
        if workflow.context and workflow.context.workspace_path:
            for step in workflow.steps:
                if "path" not in step.parameters:
                    step.parameters["path"] = str(workflow.context.workspace_path)
        
        # Adjust complexity based on intent
        if intent.complexity == "simple":
            # Remove optional steps for simple workflows
            workflow.steps = [s for s in workflow.steps if "optional" not in s.description.lower()]
        elif intent.complexity == "complex":
            # Add detailed analysis steps
            pass
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a workflow."""
        workflow = self._active_workflows.get(workflow_id)
        if not workflow:
            return None
        
        return {
            "id": workflow.id,
            "name": workflow.name,
            "status": workflow.status.value,
            "progress": self._calculate_progress(workflow),
            "steps": [
                {
                    "id": step.id,
                    "description": step.description,
                    "status": step.status.value,
                    "error": step.error
                }
                for step in workflow.steps
            ]
        }
    
    def _calculate_progress(self, workflow: Workflow) -> float:
        """Calculate workflow progress percentage."""
        if not workflow.steps:
            return 0.0
        
        completed = sum(1 for step in workflow.steps if step.status == StepStatus.COMPLETED)
        return completed / len(workflow.steps)
    
    def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a running workflow."""
        workflow = self._active_workflows.get(workflow_id)
        if workflow and workflow.status == WorkflowStatus.RUNNING:
            workflow.status = WorkflowStatus.CANCELLED
            return True
        return False
    
    def list_workflow_templates(self) -> List[Dict[str, str]]:
        """List available workflow templates."""
        return [
            {
                "id": template.id,
                "name": template.name,
                "description": template.description
            }
            for template in self._workflow_templates.values()
        ]


__all__ = [
    "WorkflowOrchestrator",
    "Workflow",
    "WorkflowStep",
    "WorkflowStatus",
    "StepStatus"
]
